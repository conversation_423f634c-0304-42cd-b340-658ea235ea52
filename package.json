{"name": "synccos-cheque-writer", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^2.37.0", "@azure/msal-common": "^13.0.0", "@azure/msal-react": "^1.5.7", "@canvasjs/charts": "^3.7.13", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.128", "@mui/material": "^5.12.1", "@mui/x-data-grid": "^6.9.0", "@mui/x-date-pickers": "^6.9.0", "@react-oauth/google": "^0.11.0", "@reduxjs/toolkit": "^1.9.5", "@tanstack/react-query": "^5.64.2", "@tanstack/react-query-devtools": "^5.64.2", "@testing-library/dom": "^9.3.1", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^4.3.0", "axios": "^1.4.0", "bootstrap": "^5.2.3", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "formik": "^2.2.9", "gsap": "^3.4.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "jquery": "^3.6.4", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "mime-types": "^2.1.35", "number-to-words": "^1.2.4", "quill-emoji": "^0.2.0", "react": "^18.2.0", "react-apple-login": "^1.1.6", "react-color": "^2.19.3", "react-date-range": "^1.4.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-otp-input": "^3.0.2", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-scripts": "^5.0.1", "react-signature-canvas": "^1.1.0-alpha.1", "react-to-print": "^2.14.12", "recharts": "^2.15.3", "redux": "^4.2.1", "uuid": "^10.0.0", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "jest": "^27.5.1", "prettier": "^2.8.7", "typescript": "^5.8.3"}}