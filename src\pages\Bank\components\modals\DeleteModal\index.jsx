import React from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, <PERSON>alog, DialogTitle, DialogContent, DialogActions, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { DeleteIcon } from "../../../../../components/Icons";
import { styles } from "../../../styles";

export const DeleteModal = ({ open, onClose, onConfirm }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: styles.dialog
      }}
    >
      <Box sx={styles.dialogHeader}>
        <DialogTitle sx={styles.dialogTitle}>
        </DialogTitle>
        <IconButton
          onClick={onClose}
          sx={styles.dialogCloseButton}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={styles.alertIconContainer}>
          <Box sx={styles.alertIconBorder}>
            <Box sx={styles.alertIconMain}>
              <DeleteIcon />
            </Box>
          </Box>
        </Box>
        <Typography sx={{ ...styles.dialogTitle, xs: { mb: '19px' }, mb: '30px' }} align="center"  >
          Permanent Account Deletion
        </Typography>
        <Typography sx={styles.dialogContent} color = "text.secondary" align="center"  >
          Are you sure you want to permanently delete this bank account? This action cannot be undone, and all related information will be deleted.
        </Typography>

      </DialogContent>
      <DialogActions sx={styles.deleteDialogActions}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={styles.cancelButton}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={styles.confirmButton}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
};
