import { useState, useRef, useEffect } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { CheckDeleteIcon, EditIcon } from "../../../../../components/Icons";
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import { styles } from "../../../styles";
import { CustomTable } from "../../../../../components/table/CustomTable";
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import { AlertModal } from "../AlertModal";
import { EditAttachmentDescriptionModal } from "../EditAttachmentDescriptionModal";
import { getFileIcon, formatFileSize } from "../../../../../common/fileUtils";

export const AttachmentsModal = ({ open, onClose, handleSave, attachments = [], title = "Add Attachments" }) => {
  const [files, setFiles] = useState(attachments || []);
  
  // Reset files state when attachments prop changes or modal opens
  useEffect(() => {
    if (open) {
      setFiles(attachments || []);
    }
  }, [attachments, open]);
  const [isDragging, setIsDragging] = useState(false);
  const [openAlertModal, setOpenAlertModal] = useState(false)
  const [openEditDescriptionModal, setOpenEditDescriptionModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [desId, setDesId] = useState(null);
  const fileInputRef = useRef(null);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  };

  const handleFileInput = (e) => {
    const selectedFiles = Array.from(e.target.files);
    if (selectedFiles.length > 0) {
      processFiles(selectedFiles);
      // Reset the file input value so the same file can be selected again
      e.target.value = '';
    }
  };

  const processFiles = (newFiles) => {
    const processedFiles = newFiles.map(file => {
      // Create URL for preview
      const url = URL.createObjectURL(file);
      return {
        name: file.name,
        size: file.size,
        type: file.type,
        url: url,
        file: file,
        id: Date.now() + Math.random().toString(36).substr(2, 9) // Add unique ID
      };
    });

    setFiles(prevFiles => [...prevFiles, ...processedFiles]);
  };

  const removeFile = (fileId) => {
    setFiles(prevFiles => {
      // Find the file with the matching ID
      const fileIndex = prevFiles.findIndex(file => file.id === fileId);

      if (fileIndex === -1) return prevFiles; // File not found

      const updatedFiles = [...prevFiles];

      // Release the object URL to avoid memory leaks
      if (updatedFiles[fileIndex]?.url) {
        URL.revokeObjectURL(updatedFiles[fileIndex].url);
      }

      // Remove the file
      updatedFiles.splice(fileIndex, 1);
      handleSave(updatedFiles);
      return updatedFiles;
    });
  };

  const handleSaveAttachments = () => {
    handleSave(files);
    onClose();
  };

  const handleDescriptionSave = (des) => {
    setFiles(prevFiles => {
      return prevFiles.map(file => {
        if (file.id === desId) {
          return { ...file, description: des };
        }
        return file;
      });
    });
    setOpenEditDescriptionModal(false);
  }

  const handleDownloadFile = (file) => {
    // Create a download link
    const link = document.createElement('a');
    
    // If it's a file object with a URL (from newly uploaded files)
    if (file.url) {
      // Set the download attributes
      link.href = file.url;
      link.download = file.name;
      
      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } 
    // If it's a file from attachments that might have a different structure
    else if (file.file) {
      // Create a blob URL from the file object
      const blobUrl = URL.createObjectURL(file.file);
      link.href = blobUrl;
      link.download = file.name;
      
      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the blob URL
      URL.revokeObjectURL(blobUrl);
    }
  };

  const renderCell = (row, column) => {
    switch (column.id) {
      case 'name':
        return (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center',
            gap: 1 
          }}>
            <Box sx={{ 
              width: '16px', 
              height: '16px', 
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {getFileIcon(row, { size: '16px' })}
            </Box>
            <Typography sx={styles.fileNameTable}>
              {row.name}
            </Typography>
          </Box>
        )
      case 'description':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton onClick = {() => {
              setDesId(row.id);
              setOpenEditDescriptionModal(true);
            }}>
              <EditIcon color="#00000099" />
            </IconButton>
            <Typography sx={{ fontSize: '16px', color: '#000000DE' }}>{row.description ? row.description : '-'}</Typography>
          </Box>
        )
      case "action":
        return (
          <Box>
            <IconButton onClick={() => handleDownloadFile(row)}>
              <FileDownloadOutlinedIcon sx={{ fontSize: '16px' }} />
            </IconButton>
            <IconButton onClick={() => {
              setDeleteId(row.id);
              setOpenAlertModal(true);
            }}>
              <CheckDeleteIcon color="#FF4D4F" />
            </IconButton>
          </Box>
        )
      default:
        return row[column.id];
    }
  }

  const renderHeaderCell = (column) => {
    switch (column.label) {
      case "File Name":
        return <Typography sx={{ textAlign: 'left', pl: 2, fontWeight: '600' }}>{column.label}</Typography>
      case "Description":
        return <Typography sx={{ textAlign: 'left', pl: 2, fontWeight: '600' }}>{column.label}</Typography>
      default:
        return column.label
    }
  }

  return (
    <>
      <CustomDialog
        open={open}
        onClose={onClose}
        title={title}
        width='765px'
        content={
          <Box>
            <Box
              sx={{
                border: isDragging ? '2px dashed #3EA5F9' : '2px dashed #204464',
                backgroundColor: isDragging ? '#F0F9FF' : '#F9FAFB',
                ...styles.attachmentDragDrop
              }}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileInput}
                style={{ display: 'none' }}
                multiple
              />
              <CloudUploadOutlinedIcon sx={{ fontSize: 48, color: '#204464', mb: 2 }} />
              <Typography variant="body1" sx={{ mb: 1, color: '#111827', fontWeight: 500, fontSize: '18px' }}>
                Drag And Drop Files here
              </Typography>
              <Typography variant="body2" sx={{ color: '#6B7280', mb: 2, fonSize: '16px' }}>
                File Format: PDF / Docx / JPG
              </Typography>
              <CustomButton
                variant="outlined"
                color="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  fileInputRef.current.click();
                }}
              >
                Upload File
              </CustomButton>
            </Box>

            {files.length > 0 && (
              <CustomTable
                columns={[
                  { id: 'name', label: 'File Name', width: '35%' },
                  { id: 'description', label: 'Description', width: '50%' },
                  { id: 'action', label: 'Action', width: '15%' },
                ]}
                data={files}
                renderCell={renderCell}
                renderHeaderCell={renderHeaderCell}
                isCenteredCells
              />
            )}
          </Box>
        }
        actions={
          <>
            {files.length > 0 && <CustomButton
              variant="outlined"
              color="primary"
              onClick={handleSaveAttachments}
            >
              Save Attachments
            </CustomButton>}
          </>
        }
      />
      <AlertModal
        open={openAlertModal}
        onClose={() => setOpenAlertModal(false)}
        type = "delete_attachment"
        onConfirm={() => {
          removeFile(deleteId);
          setOpenAlertModal(false);
        }}
      />
      <EditAttachmentDescriptionModal
        open={openEditDescriptionModal}
        onClose={() => setOpenEditDescriptionModal(false)}
        onSave={handleDescriptionSave}
        description={files.find(file => file.id === desId)?.description}
      />
    </>
  );
}
