export const validateEmail = (email) => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  if (!email) {
    return 'Email is required';
  }
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  return '';
};

export const validatePhone = (phone) => {
  const phoneRegex = /^\d{10}$/;
  if (!phone) {
    return 'Phone number is required';
  }
  if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
    return 'Please enter a valid 10-digit phone number';
  }
  return '';
};

export const handleVerificationCodeChange = (index, value, verificationCode, setVerificationCode) => {
  if (!/^\d*$/.test(value)) return;

  const newCode = [...verificationCode];
  newCode[index] = value;
  setVerificationCode(newCode);

  if (value !== '' && index < 5) {
    const nextInput = document.querySelector(`input[data-index="${index + 1}"]`);
    if (nextInput) nextInput.focus();
  }
};

export const handleVerificationCodeKeyDown = (index, e) => {
  if (e.key === 'Backspace' && !e.target.value && index > 0) {
    const prevInput = document.querySelector(`input[data-index="${index - 1}"]`);
    if (prevInput) prevInput.focus();
  }
};

export const maskEmail = (email) => {
  const [name, domain] = email.split('@');
  return `${name}@***${domain.slice(domain.lastIndexOf('.'))}`;
};

export const maskPhone = (phone) => {
  return phone.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2');
}; 

export const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const numberToWordsFull = (input) => {
  if (isNaN(input)) return 'Invalid number';

  const [whole, decimal] = input.toString().split('.');

  let words = numberToWords(parseInt(whole));

  if (decimal) {
    const decimalWords = decimal.split('').map(d => numberToWords(parseInt(d))).join(' ');
    words += ' point ' + decimalWords;
  }

  return words;
}

export const numberToCurrencyWords = (input) => {
  if (isNaN(input)) return 'Invalid number';

  const [whole, decimalRaw] = input.toString().split('.');
  const dollars = parseInt(whole);
  const cents = parseInt(decimalRaw?.padEnd(2, '0').slice(0, 2) || '0');

  let words = `${numberToWords(dollars)} dollar${dollars > 1 ? 's' : ''}`;

  if (cents > 0) {
    words += ` and ${numberToWords(cents)} cent${cents > 1 ? 's' : ''}`;
  }

  return words;
}

// Helper function for integer part
export const numberToWords = (n) => {
  const ones = [
    '', 'one', 'two', 'three', 'four', 'five', 'six',
    'seven', 'eight', 'nine', 'ten', 'eleven', 'twelve',
    'thirteen', 'fourteen', 'fifteen', 'sixteen',
    'seventeen', 'eighteen', 'nineteen'
  ];

  const tens = [
    '', '', 'twenty', 'thirty', 'forty', 'fifty',
    'sixty', 'seventy', 'eighty', 'ninety'
  ];

  const scale = ['', 'thousand', 'million', 'billion'];

  if (n === 0) return 'zero';

  function chunkToWords(num) {
    let str = '';
    if (num >= 100) {
      str += ones[Math.floor(num / 100)] + ' hundred';
      num = num % 100;
      if (num > 0) str += ' and ';
    }
    if (num >= 20) {
      str += tens[Math.floor(num / 10)];
      if (num % 10 > 0) str += '-' + ones[num % 10];
    } else if (num > 0) {
      str += ones[num];
    }
    return str;
  }

  let word = '';
  let i = 0;
  while (n > 0) {
    const chunk = n % 1000;
    if (chunk !== 0) {
      let chunkWords = chunkToWords(chunk);
      if (scale[i]) chunkWords += ' ' + scale[i];
      word = chunkWords + (word ? ' ' + word : '');
    }
    n = Math.floor(n / 1000);
    i++;
  }

  return word.trim().replace(/\s+/g, ' ').replace(/^\w/, c => c.toUpperCase());
}

 // Function to format amount as currency
 export const formatAmountAsCurrency = (value) => {
  if (!value) return '';
  // Remove any non-numeric characters except decimal point
  const numericValue = value.replace(/[^0-9.]/g, '');
  // Ensure only one decimal point
  const parts = numericValue.split('.');
  const formattedValue = parts[0] + (parts.length > 1 ? '.' + parts[1] : '');
  // Format with 2 decimal places
  return Number(formattedValue).toFixed(2);
};
