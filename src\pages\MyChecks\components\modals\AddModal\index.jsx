import { useRef, useState } from "react";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { Box, Typography, Button, Select, MenuItem, TextField, Radio, Accordion, AccordionActions, AccordionSummary, AccordionDetails, InputAdornment, IconButton, Checkbox, Divider } from "@mui/material";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { Formik } from "formik";
import * as Yup from "yup";
import { styles } from "../../../styles";
import { numberToCurrencyWords, sleep, formatAmountAsCurrency } from "../../../../../common/utils";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import CloseIcon from '@mui/icons-material/Close';
import { TagIcon } from '../../../../../components/Icons';
import { AttachmentsModal } from "../AttachmentsModal";
import { DocumentIcon } from '../../../../../components/Icons';
import AttachFileOutlinedIcon from '@mui/icons-material/AttachFileOutlined';


export const AddModal = ({ open, onClose, handleOpenUnsavedAlert, handleOpenDownloadCheckAlert, setOpenGeneratingCheckDialog }) => {
  const formRef = useRef(null);
  const [isPrinting, setIsPrinting] = useState(false);
  const [isAddCheck, setIsAddCheck] = useState(false);
  const [newChecks, setNewChecks] = useState([]);
  const [expandedAccordion, setExpandedAccordion] = useState(null);
  const [checkErrors, setCheckErrors] = useState({});
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState(false);
  const [currentCheckIndex, setCurrentCheckIndex] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);

  // Drag and drop handlers
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e, checkIndex = null) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles, checkIndex);
    }
  };

  const processFiles = (newFiles, checkIndex = null) => {
    const processedFiles = newFiles.map(file => {
      // Create URL for preview
      const url = URL.createObjectURL(file);
      return {
        name: file.name,
        size: file.size,
        type: file.type,
        url: url,
        file: file,
        id: Date.now() + Math.random().toString(36).substr(2, 9) // Add unique ID
      };
    });

    if (checkIndex !== null) {
      // Update attachments for an existing check in newChecks array
      const updatedChecks = [...newChecks];
      const currentAttachments = updatedChecks[checkIndex]?.attachments || [];
      updatedChecks[checkIndex] = {
        ...updatedChecks[checkIndex],
        attachments: [...currentAttachments, ...processedFiles]
      };
      setNewChecks(updatedChecks);
    } else if (formRef.current) {
      // Update attachments for the form
      const currentAttachments = formRef.current.values.attachments || [];
      formRef.current.setFieldValue('attachments', [...currentAttachments, ...processedFiles]);
    }
  };

  // Function to handle saving attachments from the AttachmentsModal
  const handleSaveAttachments = (attachments) => {
    if (currentCheckIndex !== null) {
      // Update attachments for an existing check in newChecks array
      const updatedChecks = [...newChecks];
      updatedChecks[currentCheckIndex] = {
        ...updatedChecks[currentCheckIndex],
        attachments: attachments
      };
      setNewChecks(updatedChecks);
    } else {
      // Update attachments for the form
      if (formRef.current) {
        formRef.current.setFieldValue('attachments', attachments);
      }
    }
  };

  // Function to open the attachments modal for a specific check
  const openAttachmentsModalForCheck = (index) => {
    setCurrentCheckIndex(index);
    setOpenAttachmentsModal(true);
  };

  // Function to open the attachments modal for the form
  const openAttachmentsModalForForm = () => {
    setCurrentCheckIndex(null);
    setOpenAttachmentsModal(true);
  };

  const initialValues = {
    bankAccount: "",
    payeeName: "",
    checkNumber: "2051",
    issuedDate: new Date().toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
    amount: "",
    memo: "",
    dollarAmount: "",
    signature: true,
    invoiceId: "",
    tags: [],
    attachments: []
  }
  const validationSchema = Yup.object().shape({
    bankAccount: Yup.string().required("Bank Account is required"),
    payeeName: Yup.string().required("Payee Name is required"),
    checkNumber: Yup.string().required("Check Number is required"),
    issuedDate: Yup.string().required("Issued Date is required"),
    amount: Yup.string().required("Amount is required"),
    dollarAmount: Yup.string().required("Dollar Amount is required"),
    signature: Yup.boolean().required("Signature is required"),
    invoiceId: Yup.string().required("Invoice Id is required"),
  })

  const otherTags = [
    { color: '#058205', value: 'Business' },
    { color: '#EF6C00', value: 'Personal' },
    { color: '#D2BA00', value: 'Miscellaneous' },
  ]
  const myTags = [
    { color: '#F03D3E', value: 'Tag1' },
    { color: '#3EA5F9', value: 'Tag2' },
    { color: '#00CF72', value: 'Tag3' },
  ]
  const handleSaveAndPrint = () => {
    setIsPrinting(true);
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }
  const handleSaveCheck = () => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }

  const handleAddCheck = () => {
    setIsAddCheck(true);
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }

  // Function to update a specific check in the newChecks array
  const updateCheck = (index, field, value) => {
    const updatedChecks = [...newChecks];
    updatedChecks[index] = {
      ...updatedChecks[index],
      [field]: value
    };

    // If amount is updated, also update dollarAmount
    if (field === 'amount') {
      updatedChecks[index].dollarAmount = numberToCurrencyWords(Number(value));
    }

    setNewChecks(updatedChecks);

    // Validate the updated check
    validateCheck(index, updatedChecks[index]);
  };



  // Function to validate a check
  const validateCheck = async (index, check) => {
    try {
      await validationSchema.validate(check, { abortEarly: false });
      // If validation passes, remove any errors for this check
      setCheckErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
    } catch (err) {
      // If validation fails, store the errors
      if (err.inner) {
        const fieldErrors = {};
        err.inner.forEach(error => {
          fieldErrors[error.path] = error.message;
        });
        setCheckErrors(prev => ({
          ...prev,
          [index]: fieldErrors
        }));
      }
    }
  };

  // Function to handle accordion change
  const handleAccordionChange = (index) => {
    // If trying to expand a different accordion while one is already expanded
    if (expandedAccordion !== null && expandedAccordion !== index) {
      // Check if the currently expanded accordion has errors
      if (checkErrors[expandedAccordion]) {
        // Don't allow changing if there are errors
        return;
      }
    }

    // If trying to collapse the currently expanded accordion and it has errors
    if (expandedAccordion === index && checkErrors[index]) {
      // Don't allow collapsing if there are errors
      return;
    }

    // Toggle the accordion
    setExpandedAccordion(expandedAccordion === index ? null : index);
  };

  // Function to remove a check
  const handleRemoveCheck = (index) => {
    const updatedChecks = [...newChecks];
    updatedChecks.splice(index, 1);
    setNewChecks(updatedChecks);

    // If the removed check was expanded, reset the expanded state
    if (expandedAccordion === index) {
      setExpandedAccordion(null);
    } else if (expandedAccordion > index) {
      // Adjust the expanded index if the removed check was before the expanded one
      setExpandedAccordion(expandedAccordion - 1);
    }
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    try {
      if (isAddCheck) {
        setNewChecks([...newChecks, { ...values, amount: Number(values.amount).toFixed(2) }]);
        setIsAddCheck(false);
      }
      else {
        onClose();
        console.log('Adding new check:', values);
        if (isPrinting) {
          setOpenGeneratingCheckDialog(true);
          await sleep(2000);
        }
      }
      resetForm();
    } catch (error) {
      console.error('Error submitting check:', error);
    } finally {
      if (isPrinting) {
        handleOpenDownloadCheckAlert();
      }
      setSubmitting(false);
    }
  };

  return (
    <>
      <CustomDialog
        open={open}
        onClose={handleOpenUnsavedAlert}
        width="1142px"
        title="Create New Check"
        content={
          <Box>
            <Typography color="#00000099" sx={{ mb: "39px" }}>
              Start a new check to manage your payments.
            </Typography>
            {
              newChecks.map((check, index) => (
                <Box key={index} sx={{ ...styles.accordionContainer, mb: expandedAccordion === index ? '24px' : '0px' }}>
                  <IconButton
                    sx={styles.closeIconButton}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent accordion from toggling
                      handleRemoveCheck(index);
                    }}
                  >
                    <CloseIcon sx={{ fontSize: '20px' }} />
                  </IconButton>
                  <Accordion
                    sx={{ ...styles.accordion, bgcolor: '#fff' }}
                    expanded={expandedAccordion === index}
                    onChange={() => handleAccordionChange(index)}
                  >
                    <AccordionSummary sx={{
                      bgcolor: expandedAccordion === index ? '#F4F5F7' : '#fff',
                      px: '32px',
                      pt: '32px',
                      pb: expandedAccordion === index ? '0' : '32px',
                      borderTopLeftRadius: '12px !important',
                      borderTopRightRadius: '12px !important',
                      mx: 0,
                      '& .MuiAccordionSummary-content': {
                        margin: 0
                      }
                    }}>
                      <Box sx={{ width: "100%" }}>
                        <Box sx={styles.accordionEditHeader}>
                          <Box sx={styles.accordionTitleContainer}>
                            <Box>
                              <Typography sx={styles.checkTitle}>
                                Haseeb Iqbal
                              </Typography>
                              <Typography sx={styles.checkSubTitle}>
                                Address Line 1, Address Line 2
                              </Typography>
                            </Box>
                            <Box sx={styles.itemContainer}>
                              <Typography sx={styles.itemLabel}>
                                Bank <br /> Account *
                              </Typography>
                              <Box>
                                <Select
                                  fullWidth
                                  name="bankAccount"
                                  value={check.bankAccount}
                                  onChange={(e) => {
                                    e.stopPropagation(); // Prevent accordion from toggling
                                    updateCheck(index, 'bankAccount', e.target.value)
                                  }
                                  }
                                  displayEmpty
                                  renderValue={(selected) => {
                                    if (!selected) {
                                      return (
                                        <Typography sx={{ color: "text.secondary" }}>
                                          Select Bank Account
                                        </Typography>
                                      );
                                    }
                                    return selected;
                                  }}
                                  sx={styles.select}
                                  error={checkErrors[index]?.bankAccount ? true : false}
                                >
                                  <MenuItem value="JP Morgan Chase." onClick={(e) => e.stopPropagation()}>
                                    JP Morgan Chase.
                                  </MenuItem>
                                  <MenuItem value="Bank of America" onClick={(e) => e.stopPropagation()}>
                                    Bank of America
                                  </MenuItem>
                                </Select>
                                {checkErrors[index]?.bankAccount && (
                                  <Typography color="error" sx={styles.errorText}>
                                    {checkErrors[index].bankAccount}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </Box>
                          <Box sx={styles.addEditHeaderRight}>
                            <Box sx={styles.itemContainer}>
                              <Typography sx={styles.itemLabel}>
                                Check <br /> Number
                              </Typography>
                              <TextField
                                fullWidth
                                name="checkNumber"
                                placeholder="Check Number"
                                value={check.checkNumber}
                                disabled={true}
                                sx={{
                                  ...styles.input,
                                  "& .MuiInputBase-input": {
                                    color: "#CECECE",
                                  },
                                  "& .MuiInputLabel-root": {
                                    color: "#CECECE",
                                  },
                                }}
                              />
                              <Typography sx={styles.checkNumberAutomatic} >
                                (Automatic)
                              </Typography>
                            </Box>


                          </Box>
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails sx={{
                      bgcolor: expandedAccordion === index ? '#F4F5F7' : '#fff',
                      px: '32px',
                      pb: '32px',
                      borderBottom: '1px solid #E0E0E0',
                      borderBottomLeftRadius: '12px !important',
                      borderBottomRightRadius: '12px !important',
                      mb: '24px'
                    }}>
                      <Box sx={{ ...styles.accordionEditHeader, mt: '-5px', mb: '24px' }}>
                        <Box>
                          <Typography sx={{ ...styles.checkSubTitle, mb: '24px' }}>
                            ashkasham, Badakhshan, 23123
                          </Typography>
                          <Typography sx={styles.payeeNameLabel}>Payee Name</Typography>

                          <Select
                            fullWidth
                            name="payeeName"
                            value={check.payeeName}
                            onChange={(e) => updateCheck(index, 'payeeName', e.target.value)}
                            displayEmpty
                            renderValue={(selected) => {
                              if (!selected) {
                                return (
                                  <Typography sx={{ color: "text.secondary" }}>
                                    Select Payee Name
                                  </Typography>
                                );
                              }
                              return selected;
                            }}
                            sx={styles.payeeSelect}
                            error={checkErrors[index]?.payeeName ? true : false}
                          >
                            <MenuItem value="Abraham Sabel">
                              Abraham Sabel
                            </MenuItem>
                            <MenuItem value="Andrew">
                              Andrew
                            </MenuItem>
                          </Select>
                          {checkErrors[index]?.payeeName && (
                            <Typography color="error" sx={styles.errorText}>
                              {checkErrors[index].payeeName}
                            </Typography>
                          )}
                        </Box>
                        <Box sx={styles.accordionEditHeaderRight}>
                          <Box sx={styles.itemContainer}>
                            <Typography sx={styles.itemLabel}>
                              Issued <br /> Date
                            </Typography>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DatePicker
                                value={check.issuedDate ? dayjs(check.issuedDate) : null}
                                onChange={(newValue) => {
                                  const formattedDate = newValue ? newValue.format('MM/DD/YYYY') : '';
                                  updateCheck(index, 'issuedDate', formattedDate);
                                }}
                                slotProps={{
                                  textField: {
                                    fullWidth: true,
                                    placeholder: "Issued Date",
                                    error: checkErrors[index]?.issuedDate ? true : false,
                                    helperText: checkErrors[index]?.issuedDate || '',
                                    sx: styles.input
                                  }
                                }}
                              />
                            </LocalizationProvider>
                          </Box>

                          <Box sx={{ ...styles.itemContainer, alignItems: 'center' }}>
                            <Typography sx={styles.itemLabel}>
                              Amount
                            </Typography>
                            <TextField
                              fullWidth
                              name="amount"
                              placeholder="Amount"
                              value={check.amount}
                              onChange={(e) => {
                                const value = e.target.value.replace(/[^0-9.]/g, '');
                                if (/^\d*\.?\d*$/.test(value)) {
                                  updateCheck(index, 'amount', value);
                                }
                              }}
                              onBlur={(e) => {
                                const formattedValue = formatAmountAsCurrency(e.target.value);
                                updateCheck(index, 'amount', formattedValue);
                              }}
                              sx={styles.input}
                              error={checkErrors[index]?.amount ? true : false}
                              helperText={checkErrors[index]?.amount || ''}
                              InputProps={{
                                startAdornment: <InputAdornment position="start">$</InputAdornment>,
                                inputMode: 'decimal',
                                pattern: '[0-9]*\\.?[0-9]*'
                              }}
                            />
                          </Box>
                        </Box>
                      </Box>
                      <Box sx={styles.dollarsContainer}>
                        <TextField
                          fullWidth
                          name="dollarAmount"
                          placeholder=""
                          value={check.dollarAmount}
                          onChange={(e) => updateCheck(index, 'dollarAmount', e.target.value)}
                          sx={styles.dollarInput}
                          error={checkErrors[index]?.dollarAmount ? true : false}
                          helperText={checkErrors[index]?.dollarAmount || ''}
                        />
                        <Typography sx={styles.dollarText}>
                          Dollars
                        </Typography>
                      </Box>

                      <Box sx={styles.memoContainer}>
                        <Box>
                          <Typography sx={styles.memoLabel}>Memo</Typography>
                          <TextField
                            fullWidth
                            name="memo"
                            placeholder=""
                            value={check.memo}
                            onChange={(e) => updateCheck(index, 'memo', e.target.value)}
                            sx={styles.memoInput}
                            error={checkErrors[index]?.memo ? true : false}
                            helperText={checkErrors[index]?.memo || ''}
                          />
                        </Box>
                        <Box>
                          <Typography sx={styles.checkTitle}>
                            Your Signature
                          </Typography>
                          <Typography sx={styles.checkSubTitle}>
                            Want to sign with signature
                          </Typography>

                          <Box sx={styles.radioGroup}>
                            <Box sx={styles.radioItem}>
                              <Radio
                                name="signature"
                                value={true}
                                checked={check.signature === true}
                                onChange={() => updateCheck(index, 'signature', true)}
                                sx={styles.radio}
                              />
                              <Typography sx={styles.radioLabel}>Yes</Typography>
                            </Box>
                            <Box sx={styles.radioItem}>
                              <Radio
                                name="signature"
                                value={false}
                                checked={check.signature === false}
                                onChange={() => updateCheck(index, 'signature', false)}
                                sx={styles.radio}
                              />
                              <Typography sx={styles.radioLabel}>No</Typography>
                            </Box>
                          </Box>
                          {checkErrors[index]?.signature && (
                            <Typography color="error" sx={styles.errorText}>
                              {checkErrors[index].signature}
                            </Typography>
                          )}
                        </Box>
                      </Box>

                      <Typography sx={styles.footerNumber}>
                        {`{“000012”} {“1234567890”} {“1234567890”}`}
                      </Typography>
                    </AccordionDetails>
                    <Box sx={styles.checkBottomContainer}>
                      <Box sx={styles.checkBottomItemContainer}>
                        <Typography sx={styles.checkBottomItemlabel}>Tags</Typography>
                        <Select
                          fullWidth
                          name="tags"
                          value={check.tags}
                          onChange={(e) => updateCheck(index, 'tags', e.target.value)}
                          displayEmpty
                          multiple
                          renderValue={(selected) => {
                            if (!selected || selected.length === 0) {
                              return (
                                <Typography sx={{ color: "text.secondary" }}>
                                  Select Tags
                                </Typography>
                              );
                            }
                            return <Box sx={{ display: "flex", gap: '12px', alignItems: 'center' }}>
                              {
                                selected.slice(0, 3).map((tag, index) =>
                                  <Box key={"tag_input" + index} sx={styles.selectTagItem}>
                                    <Box sx={{ ...styles.tag, backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color }} />
                                    {tag}
                                  </Box>)
                              }
                              {selected.length > 3 && <Typography sx={{ color: "#000000DE" }}>+{selected.length - 3}</Typography>}
                            </Box>;
                          }}
                          error={checkErrors[index]?.tags ? true : false}
                          sx={{ ...styles.select, width: '100% !important' }}
                          MenuProps={{
                            sx: {
                              '& .MuiMenu-paper': {
                                width: '210px',
                                minWidth: '210px !important'
                              }
                            },
                            anchorOrigin: {
                              vertical: 'bottom',
                              horizontal: 'right'
                            },
                            transformOrigin: {
                              vertical: 'top',
                              horizontal: 'right'
                            },
                          }}
                        >
                          <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                            <TagIcon />
                            <Typography sx={styles.tagTitle}>Other Tags</Typography>
                          </Box>
                          {
                            otherTags.map((tag, index) => (
                              <MenuItem key={index} value={tag.value}>
                                <Box sx={styles.tagItem}>
                                  <Checkbox checked={(check.tags || []).includes(tag.value)} />
                                  <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                  <Typography sx={styles.tagText}>{tag.value}</Typography>
                                </Box>
                              </MenuItem>
                            ))
                          }
                          <Divider sx={{ m: 0 }} />
                          <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                            <TagIcon />
                            <Typography sx={styles.tagTitle}>My Tags</Typography>
                          </Box>
                          {
                            myTags.map((tag, index) => (
                              <MenuItem key={index} value={tag.value}>
                                <Box sx={styles.tagItem}>
                                  <Checkbox checked={(check.tags || []).includes(tag.value)} />
                                  <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                  <Typography sx={styles.tagText}>{tag.value}</Typography>
                                </Box>
                              </MenuItem>
                            ))
                          }

                        </Select>
                        {checkErrors[index]?.tags && (
                          <Typography color="error" sx={styles.errorText}>
                            {checkErrors[index]?.tags}
                          </Typography>
                        )}
                      </Box>
                      <Box sx={styles.checkBottomItemContainer}>
                        <Typography sx={styles.checkBottomItemlabel}>Invoice ID</Typography>
                        <TextField
                          fullWidth
                          name="invoiceId"
                          placeholder="Invoice Id"
                          value={check.invoiceId}
                          onChange={(e) => updateCheck(index, 'invoiceId', e.target.value)}
                          error={checkErrors[index]?.invoiceId ? true : false}
                          helperText={checkErrors[index]?.invoiceId}
                          sx={{
                            ...styles.input,
                            width: '100% !important'
                          }}
                        />
                      </Box>

                      <Box sx={{display: 'flex'}}>
                        <Box 
                          sx={{
                            ...styles.addAttachmentButton,
                            borderColor: check.attachments.length > 0 ? '#204464' : '#00000099',
                            borderStyle: isDragging ? 'dashed' : 'dashed',
                            borderWidth: isDragging ? '2px' : '1px',
                            backgroundColor: isDragging ? '#F0F9FF' : 'transparent',
                          }}
                          onDragEnter={handleDragEnter}
                          onDragLeave={handleDragLeave}
                          onDragOver={handleDragOver}
                          onDrop={(e) => {
                            e.stopPropagation(); // Prevent accordion from toggling
                            handleDrop(e, index);
                          }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent accordion from toggling
                            openAttachmentsModalForCheck(index);
                          }}
                        >
                          <DocumentIcon color={check.attachments.length > 0 ? '#204464' : '#00000099'} />
                          <Typography sx={{fontSize: '18px', color: check.attachments.length > 0 ? '#204464' : '#00000099', lineHeight: 'normal'}}>
                            {check.attachments && check.attachments.length > 0 
                              ? `${check.attachments.length} Attachments` 
                              : 'Add Attachments'}
                          </Typography>
                          <AttachFileOutlinedIcon sx={{fontSize: '20px', color: check.attachments.length > 0 ? '#204464' : '#00000099', rotate: '45deg' }} />
                        </Box>
                      </Box>
                    </Box>
                  </Accordion>
                </Box>
              ))
            }
            <Formik
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
              innerRef={formRef}
            >
              {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
                <Accordion sx={styles.addAccordionContainer}>
                  <Box sx={styles.formContainer}>
                    <Box sx={styles.addCheckFormContainer}>
                      <Box sx={styles.addFormHeader}>
                        <Box>
                          <Box sx={styles.titleContainer}>
                            <Box>
                              <Typography sx={styles.checkTitle}>
                                Haseeb Iqbal
                              </Typography>
                              <Typography sx={styles.checkSubTitle}>
                                Address Line 1, Address Line 2 <br />
                                ashkasham, Badakhshan, 23123
                              </Typography>
                            </Box>
                            <Box sx={styles.itemContainer}>
                              <Typography sx={styles.itemLabel}>
                                Bank <br /> Account *
                              </Typography>
                              <Box>
                                <Select
                                  fullWidth
                                  name="bankAccount"
                                  value={values.bankAccount}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  displayEmpty
                                  renderValue={(selected) => {
                                    if (!selected) {
                                      return (
                                        <Typography sx={{ color: "text.secondary" }}>
                                          Select Bank Account
                                        </Typography>
                                      );
                                    }
                                    return selected;
                                  }}
                                  error={touched.bankAccount && !!errors.bankAccount}
                                  sx={styles.select}
                                >
                                  <MenuItem value="JP Morgan Chase.">
                                    JP Morgan Chase.
                                  </MenuItem>
                                  <MenuItem value="Bank of America">
                                    Bank of America
                                  </MenuItem>
                                </Select>
                                {touched.bankAccount && errors.bankAccount && (
                                  <Typography color="error" sx={styles.errorText}>
                                    {errors.bankAccount}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </Box>
                          <Typography sx={styles.payeeNameLabel}>Payee Name</Typography>

                          <Select
                            fullWidth
                            name="payeeName"
                            value={values.payeeName}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            displayEmpty
                            renderValue={(selected) => {
                              if (!selected) {
                                return (
                                  <Typography sx={{ color: "text.secondary" }}>
                                    Select Payee Name
                                  </Typography>
                                );
                              }
                              return selected;
                            }}
                            error={touched.payeeName && !!errors.payeeName}
                            sx={styles.payeeSelect}
                          >
                            <MenuItem value="Abraham Sabel">
                              Abraham Sabel
                            </MenuItem>
                            <MenuItem value="Andrew">
                              Andrew
                            </MenuItem>
                          </Select>

                          {touched.payeeName && errors.payeeName && (
                            <Typography color="error" sx={styles.errorText}>
                              {errors.payeeName}
                            </Typography>
                          )}

                        </Box>
                        <Box sx={styles.addEditHeaderRight}>
                          <Box sx={styles.itemContainer}>
                            <Typography sx={styles.itemLabel}>
                              Check <br /> Number
                            </Typography>
                            <TextField
                              fullWidth
                              name="checkNumber"
                              placeholder="Check Number"
                              value={values.checkNumber}
                              onChange={handleChange}
                              onBlur={handleBlur}
                              error={touched.checkNumber && !!errors.checkNumber}
                              helperText={touched.checkNumber && errors.checkNumber}
                              disabled={true}
                              sx={{
                                ...styles.input,
                                "& .MuiInputBase-input": {
                                  color: "#CECECE",
                                },
                                "& .MuiInputLabel-root": {
                                  color: "#CECECE",
                                },
                              }}
                            />
                            <Typography sx={styles.checkNumberAutomatic} >
                              (Automatic)
                            </Typography>
                          </Box>

                          <Box sx={styles.itemContainer}>
                            <Typography sx={styles.itemLabel}>
                              Issued <br /> Date
                            </Typography>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DatePicker
                                value={values.issuedDate ? dayjs(values.issuedDate) : null}
                                onChange={(newValue) => {
                                  const formattedDate = newValue ? newValue.format('MM/DD/YYYY') : '';
                                  setFieldValue('issuedDate', formattedDate);
                                }}
                                slotProps={{
                                  textField: {
                                    fullWidth: true,
                                    placeholder: "Issued Date",
                                    error: touched.issuedDate && !!errors.issuedDate,
                                    helperText: touched.issuedDate && errors.issuedDate,
                                    sx: styles.input
                                  }
                                }}
                              />
                            </LocalizationProvider>
                          </Box>

                          <Box sx={{ ...styles.itemContainer, alignItems: 'center' }}>
                            <Typography sx={styles.itemLabel}>
                              Amount
                            </Typography>
                            <TextField
                              fullWidth
                              name="amount"
                              placeholder="Amount"
                              value={values.amount}
                              onChange={(e) => {
                                // Only allow numbers and one decimal point
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                  handleChange(e);
                                  setFieldValue("dollarAmount", numberToCurrencyWords(Number(value)));
                                }
                              }}
                              onBlur={(e) => {
                                const formattedValue = formatAmountAsCurrency(e.target.value);
                                setFieldValue('amount', formattedValue);
                                setFieldValue("dollarAmount", numberToCurrencyWords(Number(formattedValue)));
                              }}
                              error={touched.amount && !!errors.amount}
                              helperText={touched.amount && errors.amount}
                              sx={styles.input}
                              InputProps={{
                                startAdornment: <InputAdornment position="start">$</InputAdornment>,
                                inputMode: 'decimal',
                                pattern: '[0-9]*\\.?[0-9]*'
                              }}
                            />
                          </Box>

                        </Box>
                      </Box>

                      <Box sx={styles.dollarsContainer}>
                        <TextField
                          fullWidth
                          name="dollarAmount"
                          placeholder=""
                          value={values.dollarAmount}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.dollarAmount && !!errors.dollarAmount}
                          helperText={touched.dollarAmount && errors.dollarAmount}
                          sx={styles.dollarInput}
                        />
                        <Typography sx={styles.dollarText}>
                          Dollars
                        </Typography>
                      </Box>

                      <Box sx={styles.memoContainer}>
                        <Box>
                          <Typography sx={styles.memoLabel}>Memo</Typography>
                          <TextField
                            fullWidth
                            name="memo"
                            placeholder=""
                            value={values.memo}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.memo && !!errors.memo}
                            helperText={touched.memo && errors.memo}
                            sx={styles.memoInput}
                          />
                        </Box>
                        <Box>
                          <Typography sx={styles.checkTitle}>
                            Your Signature
                          </Typography>
                          <Typography sx={styles.checkSubTitle}>
                            Want to sign with signature
                          </Typography>

                          <Box sx={styles.radioGroup}>
                            <Box sx={styles.radioItem}>
                              <Radio
                                name="signature"
                                value={true}
                                checked={values.signature === true}
                                onChange={(e) => {
                                  setFieldValue("signature", true);
                                }}
                                sx={styles.radio}
                              />
                              <Typography sx={styles.radioLabel}>Yes</Typography>
                            </Box>
                            <Box sx={styles.radioItem}>
                              <Radio
                                name="signature"
                                value={false}
                                checked={values.signature === false}
                                onChange={(e) => {
                                  setFieldValue("signature", false);
                                }}
                                sx={styles.radio}
                              />
                              <Typography sx={styles.radioLabel}>No</Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Box>

                      <Typography sx={styles.footerNumber}>
                        {`{“000012”} {“1234567890”} {“1234567890”}`}
                      </Typography>
                    </Box>

                    <Box sx={styles.checkBottomContainer}>
                      <Box sx={styles.checkBottomItemContainer}>
                        <Typography sx={styles.checkBottomItemlabel}>Tags</Typography>
                        <Select
                          fullWidth
                          name="tags"
                          value={values.tags}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          displayEmpty
                          multiple
                          renderValue={(selected) => {
                            if (!selected || selected.length === 0) {
                              return (
                                <Typography sx={{ color: "text.secondary" }}>
                                  Select Tags
                                </Typography>
                              );
                            }
                            return <Box sx={{ display: "flex", gap: '12px', alignItems: 'center' }}>
                              {
                                selected.slice(0, 3).map((tag, index) =>
                                  <Box key={"tag_input" + index} sx={styles.selectTagItem}>
                                    <Box sx={{ ...styles.tag, backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color }} />
                                    {tag}
                                  </Box>)
                              }
                              {selected.length > 3 && <Typography sx={{ color: "#000000DE" }}>+{selected.length - 3}</Typography>}
                            </Box>;
                          }}
                          error={touched.tags && !!errors.tags}
                          sx={{ ...styles.select, width: '100% !important' }}
                          MenuProps={{
                            sx: {
                              '& .MuiMenu-paper': {
                                width: '210px',
                                minWidth: '210px !important'
                              }
                            },
                            anchorOrigin: {
                              vertical: 'bottom',
                              horizontal: 'right'
                            },
                            transformOrigin: {
                              vertical: 'top',
                              horizontal: 'right'
                            },
                          }}
                        >
                          <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                            <TagIcon />
                            <Typography sx={styles.tagTitle}>Other Tags</Typography>
                          </Box>
                          {
                            otherTags.map((tag, index) => (
                              <MenuItem key={index} value={tag.value}>
                                <Box sx={styles.tagItem}>
                                  <Checkbox checked={(values.tags || []).includes(tag.value)} />
                                  <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                  <Typography sx={styles.tagText}>{tag.value}</Typography>
                                </Box>
                              </MenuItem>
                            ))
                          }
                          <Divider sx={{ m: 0 }} />
                          <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                            <TagIcon />
                            <Typography sx={styles.tagTitle}>My Tags</Typography>
                          </Box>
                          {
                            myTags.map((tag, index) => (
                              <MenuItem key={index} value={tag.value}>
                                <Box sx={styles.tagItem}>
                                  <Checkbox checked={(values.tags || []).includes(tag.value)} />
                                  <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                  <Typography sx={styles.tagText}>{tag.value}</Typography>
                                </Box>
                              </MenuItem>
                            ))
                          }

                        </Select>
                        {touched.tags && errors.tags && (
                          <Typography color="error" sx={styles.errorText}>
                            {errors.tags}
                          </Typography>
                        )}
                      </Box>
                      <Box sx={styles.checkBottomItemContainer}>
                        <Typography sx={styles.checkBottomItemlabel}>Invoice ID</Typography>
                        <TextField
                          fullWidth
                          name="invoiceId"
                          placeholder="Invoice Id"
                          value={values.invoiceId}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.invoiceId && !!errors.invoiceId}
                          helperText={touched.invoiceId && errors.invoiceId}
                          sx={{
                            ...styles.input,
                            width: '100% !important'
                          }}
                        />
                      </Box>
                      <Box sx={{display: 'flex'}}>
                        <Box 
                          sx={{
                            ...styles.addAttachmentButton,
                            borderColor: values.attachments.length > 0 ? '#204464' : '#00000099',
                            borderStyle: isDragging ? 'dashed' : 'dashed',
                            borderWidth: isDragging ? '2px' : '1px',
                            backgroundColor: isDragging ? '#F0F9FF' : 'transparent',
                          }}
                          onDragEnter={handleDragEnter}
                          onDragLeave={handleDragLeave}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e)}
                          onClick={(e) => {
                            e.stopPropagation();
                            openAttachmentsModalForForm();
                          }}
                        >
                          <DocumentIcon color={values.attachments.length > 0 ? '#204464' : '#00000099'} />
                          <Typography sx={{fontSize: '18px', color: values.attachments.length > 0 ? '#204464' : '#00000099', lineHeight: 'normal'}}>
                            {values.attachments && values.attachments.length > 0 
                              ? `${values.attachments.length} Attachments` 
                              : 'Add Attachments'}
                          </Typography>
                          <AttachFileOutlinedIcon sx={{fontSize: '20px', color: values.attachments.length > 0 ? '#204464' : '#00000099', rotate: '45deg' }} />
                        </Box>
                      </Box>
                    </Box>
                  </Box>

                </Accordion>
              )}
            </Formik>
            <Button sx={styles.addCheckButton} onClick={handleAddCheck}>
              Add Check
            </Button>
          </Box>
        }
        actions={
          <Box sx={styles.actionsContainer}>
            <Button
              onClick={handleSaveAndPrint}
              variant="outlined"
              sx={styles.cancelButton}
            >
              Save & Print
            </Button>

            <CustomButton
              variant="outlined"
              color="primary"
              onClick={handleSaveCheck}
            >
              Save Check
            </CustomButton>
          </Box>
        }
      />

      <AttachmentsModal
        open={openAttachmentsModal}
        onClose={() => setOpenAttachmentsModal(false)}
        title="Add Attachments"
        handleSave={handleSaveAttachments}
        attachments={currentCheckIndex !== null 
          ? (newChecks[currentCheckIndex]?.attachments || []) 
          : (formRef.current?.values.attachments || [])}
      />
    </>
  );
};
