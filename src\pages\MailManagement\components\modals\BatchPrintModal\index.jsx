import {
  Box,
  Typography,
  Button,
} from "@mui/material";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { styles } from "../../../styles";

import { CustomTable } from "../../../../../components/table/CustomTable";
import { CustomButton } from "../../../../../components/buttons/CustomButton";

export const BatchPrintModal = ({ open, onClose, checks, onConfirm }) => {
  const handleNext = () => {
    onClose();
    onConfirm(checks);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      width={765}
      title="Batch Print Confirmation"
      content={
        <Box sx={styles.content}>
          <Box sx={styles.section}>
            <Typography sx={styles.dialogDescription}>
              By submitting this batch, a charge <span style = {{color : '#204464'}}>$5.59</span> will be applied to the customer’s account. Please ensure everything is correct before proceeding.
            </Typography>
            <Typography sx={styles.checksCount}>Number of Checks: {checks?.length}</Typography>
            <CustomTable
              columns={[
                { id: "no", label: "Check No." },
                { id: "amount", label: "Check Amount" },
                { id: "payeeName", label: "Payee Name"},
                {
                  id: "account",
                  label: "User Account",
                },
                { id: "issuedDate", label: "Issued Date" },
              ]}
              data={checks}
              isCenteredCells={true}
            />
          </Box>
        </Box>
      }
      actions={
        <>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={styles.cancelButton}
          >
            Cancel
          </Button>

          <CustomButton
            variant="outlined"
            color="primary"
            onClick={handleNext}
          >
            Print Batch
          </CustomButton>
        </>
      }
    />
  );
};
