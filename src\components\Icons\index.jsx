export const MarkIcon = () => {
  return (
    <svg
      width="14"
      height="12"
      viewBox="0 0 14 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.2087 0.946326C10.1472 0.859894 10.0659 0.789436 9.97157 0.740841C9.87728 0.692246 9.77273 0.666924 9.66665 0.666992H0.999983C0.87756 0.667103 0.75753 0.700922 0.653057 0.76474C0.548583 0.828559 0.463699 0.919913 0.407714 1.02879C0.351728 1.13766 0.326802 1.25984 0.33567 1.38195C0.344537 1.50405 0.386855 1.62135 0.457983 1.72099L3.51398 6.00033L0.457317 10.2797C0.386156 10.3793 0.343832 10.4967 0.334991 10.6189C0.32615 10.741 0.351134 10.8633 0.4072 10.9722C0.463266 11.0811 0.548247 11.1724 0.652817 11.2362C0.757386 11.2999 0.877503 11.3337 0.999983 11.3337H9.66665C9.77273 11.3337 9.87728 11.3084 9.97157 11.2598C10.0659 11.2112 10.1472 11.1408 10.2087 11.0543L13.542 6.38766C13.623 6.27474 13.6665 6.13928 13.6665 6.00033C13.6665 5.86137 13.623 5.72591 13.542 5.61299L10.2087 0.946326ZM9.32332 10.0003H2.29532L4.87532 6.38766C4.95629 6.27474 4.99984 6.13928 4.99984 6.00033C4.99984 5.86137 4.95629 5.72591 4.87532 5.61299L2.29532 2.00033H9.32332L12.1807 6.00033L9.32332 10.0003Z"
        fill="#204464"
      />
    </svg>
  );
};

export const GuardIcon = ({ width = "24px", height = "24px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.91 11.1203C20.8494 13.5835 20.0009 15.9623 18.4892 17.9078C16.9774 19.8534 14.8818 21.2632 12.51 21.9303C12.176 22.0204 11.824 22.0204 11.49 21.9303C9.11818 21.2632 7.02264 19.8534 5.51084 17.9078C3.99905 15.9623 3.1506 13.5835 3.09 11.1203L3.09 6.73034C3.11162 6.29281 3.25259 5.86955 3.49761 5.50642C3.74263 5.1433 4.08237 4.85416 4.48 4.67034L10.05 2.39034C11.3034 1.8803 12.7066 1.8803 13.96 2.39034L19.53 4.67034C19.9262 4.85604 20.2647 5.14565 20.5095 5.50839C20.7542 5.87112 20.8961 6.29341 20.92 6.73034L20.91 11.1203Z"
      stroke="black"
      strokeOpacity="0.87"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 10.5C14 10.8956 13.8827 11.2822 13.6629 11.6111C13.4432 11.94 13.1308 12.1964 12.7654 12.3478C12.3999 12.4991 11.9978 12.5387 11.6098 12.4616C11.2219 12.3844 10.8655 12.1939 10.5858 11.9142C10.3061 11.6345 10.1156 11.2781 10.0384 10.8902C9.96126 10.5022 10.0009 10.1001 10.1522 9.73463C10.3036 9.36918 10.56 9.05682 10.8889 8.83706C11.2178 8.6173 11.6044 8.5 12 8.5C12.5304 8.5 13.0391 8.71071 13.4142 9.08579C13.7893 9.46086 14 9.96957 14 10.5Z"
      stroke="black"
      strokeOpacity="0.87"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 12.5V15.5"
      stroke="black"
      strokeOpacity="0.87"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MoreVerticalIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailIcon = ({ width = "16px", height = "16px", color = "#204464" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z"
      stroke={color}
      strokeOpacity="0.87"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 9L13.87 11.5C13.3265 11.8995 12.6696 12.1149 11.995 12.1149C11.3204 12.1149 10.6635 11.8995 10.12 11.5L7 9"
      stroke={color}
      strokeOpacity="0.87"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PhoneIcon = ({ width = "24px", height = "24px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M43.9402 36.66C43.9372 37.4148 43.7665 38.1594 43.4402 38.84C43.091 39.584 42.6326 40.2715 42.0802 40.88C41.1795 41.9047 40.058 42.7117 38.8002 43.24C37.5638 43.7505 36.2379 44.0089 34.9002 44C32.6506 43.9584 30.4327 43.4618 28.3802 42.54C25.9446 41.4842 23.6366 40.1557 21.5002 38.58C19.1764 36.8802 16.9836 35.0083 14.9402 32.98C12.9178 30.944 11.0524 28.7578 9.36022 26.44C7.80602 24.3182 6.49135 22.031 5.44022 19.62C4.52739 17.5584 4.03772 15.3344 4.00022 13.08C3.99162 11.7592 4.23602 10.4489 4.72022 9.22003C5.22891 7.94948 6.01467 6.80843 7.02022 5.88003C7.55257 5.30427 8.19505 4.84121 8.90962 4.51828C9.62419 4.19535 10.3963 4.01912 11.1802 4.00003C11.7403 3.99781 12.2938 4.12081 12.8002 4.36003C13.3404 4.60455 13.8037 4.99177 14.1402 5.48003L18.7802 12.02C19.1011 12.4535 19.3696 12.9235 19.5802 13.42C19.7539 13.804 19.8491 14.2188 19.8602 14.64C19.8546 15.1431 19.7091 15.6348 19.4402 16.06C19.1318 16.5811 18.7551 17.0587 18.3202 17.48L16.8002 19.06C16.6943 19.1639 16.6112 19.2887 16.5561 19.4264C16.501 19.5641 16.4752 19.7118 16.4802 19.86C16.4805 20.0153 16.5006 20.1699 16.5402 20.32C16.6002 20.48 16.6602 20.6 16.7002 20.72C17.2373 21.6306 17.8601 22.4879 18.5602 23.28C19.4602 24.32 20.4202 25.38 21.4602 26.44C22.5402 27.5 23.5802 28.48 24.6402 29.38C25.4344 30.0807 26.299 30.6973 27.2202 31.22C27.3202 31.26 27.4402 31.32 27.5802 31.38C27.7405 31.4373 27.91 31.4644 28.0802 31.46C28.233 31.4634 28.3849 31.435 28.526 31.3764C28.6672 31.3179 28.7947 31.2306 28.9002 31.12L30.4202 29.62C30.8417 29.177 31.3271 28.7995 31.8602 28.5C32.2843 28.2286 32.7767 28.083 33.2802 28.08C33.7 28.0858 34.1145 28.1742 34.5002 28.34C34.9926 28.5509 35.4618 28.8123 35.9002 29.12L42.5202 33.82C43.0006 34.1341 43.382 34.5779 43.6202 35.1C43.8277 35.5941 43.9364 36.1241 43.9402 36.66Z"
      stroke="black"
      strokeOpacity="0.87"
      strokeWidth="3"
    />
  </svg>
);

export const MailboxIcon = ({ width = "16px", height = "16px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    stroke="black"
    strokeOpacity="0.87"
  >
    <path
      d="M1.33325 5.66634C1.33325 3.33301 2.66659 2.33301 4.66659 2.33301H11.3333C13.3333 2.33301 14.6666 3.33301 14.6666 5.66634V10.333C14.6666 12.6663 13.3333 13.6663 11.3333 13.6663H4.66659"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3334 6L9.24675 7.66667C8.88439 7.93299 8.44645 8.07662 7.99675 8.07662C7.54705 8.07662 7.1091 7.93299 6.74675 7.66667L4.66675 6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.33325 11H5.33325"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.33325 8.33301H3.33325"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FilterIcon = ({ width = "16px", height = "18px", color = "white" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 16 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5002 0.75H13.5002C13.9418 0.751319 14.365 0.927337 14.6773 1.23961C14.9895 1.55189 15.1655 1.97504 15.1669 2.41667V4.25C15.1184 4.96587 14.824 5.643 14.3335 6.16667L10.7502 9.33333C10.4914 9.58183 10.2846 9.87919 10.1415 10.2082C9.99848 10.5372 9.92212 10.8913 9.91686 11.25V14.8333C9.90568 15.1105 9.8325 15.3817 9.7027 15.6268C9.57291 15.872 9.38981 16.0849 9.16686 16.25L8.0002 17C7.74511 17.1558 7.45341 17.2416 7.15458 17.2485C6.85575 17.2555 6.56036 17.1835 6.29827 17.0398C6.03618 16.8961 5.81666 16.6857 5.6619 16.43C5.50714 16.1742 5.42261 15.8822 5.41687 15.5833V11.1667C5.37287 10.5302 5.14084 9.9211 4.7502 9.41667L1.58353 6.08333C1.15167 5.62896 0.887193 5.04123 0.833532 4.41667V2.5C0.825748 2.27508 0.862924 2.05086 0.942883 1.84048C1.02284 1.63011 1.14397 1.4378 1.29919 1.27483C1.4544 1.11186 1.64057 0.981493 1.8468 0.891373C2.05303 0.801254 2.27516 0.753191 2.5002 0.75V0.75Z"
      stroke={color}
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.10833 0.75L3 7.33333"
      stroke={color}
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DownloadAsFdfIcon = ({ width = "24px", height = "24px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    stroke="black"
    strokeOpacity="0.87"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M3.19466 13.1654H8.80533C9.42417 13.1654 10.0177 12.9195 10.4552 12.4819C10.8928 12.0444 11.1387 11.4509 11.1387 10.832V7.14536C11.1389 6.5266 10.8933 5.93309 10.456 5.49536L6.47666 1.51536C6.25997 1.2987 6.00273 1.12684 5.71963 1.00959C5.43652 0.892344 5.13309 0.832009 4.82666 0.832031H3.19466C2.57582 0.832031 1.98233 1.07786 1.54475 1.51545C1.10716 1.95303 0.861328 2.54653 0.861328 3.16536V10.832C0.861328 11.4509 1.10716 12.0444 1.54475 12.4819C1.98233 12.9195 2.57582 13.1654 3.19466 13.1654Z" />
    <path d="M5.79297 1.07227V4.8456C5.79297 5.19922 5.93344 5.53836 6.18349 5.78841C6.43354 6.03846 6.77268 6.17893 7.1263 6.17893H10.901" />
    <path d="M2.83203 9.99805V9.33138M2.83203 9.33138V7.99805H3.4987C3.67551 7.99805 3.84508 8.06828 3.9701 8.19331C4.09513 8.31833 4.16536 8.4879 4.16536 8.66471C4.16536 8.84152 4.09513 9.01109 3.9701 9.13612C3.84508 9.26114 3.67551 9.33138 3.4987 9.33138H2.83203ZM8.16536 9.99805V9.16471M8.16536 9.16471V7.99805H9.16536M8.16536 9.16471H9.16536M5.4987 9.99805V7.99805H5.83203C6.09725 7.99805 6.3516 8.1034 6.53914 8.29094C6.72667 8.47848 6.83203 8.73283 6.83203 8.99805C6.83203 9.26326 6.72667 9.51762 6.53914 9.70515C6.3516 9.89269 6.09725 9.99805 5.83203 9.99805H5.4987Z" />
  </svg>
);

export const DeleteIcon = ({ width = "47px", height = "51px", color = "#F03D3E" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 47 51"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.54288 42.4533L8.6915 42.433L8.54288 42.4533C8.83382 44.5855 9.88746 46.5401 11.5087 47.9551C13.13 49.3702 15.209 50.15 17.361 50.15H29.639C31.791 50.15 33.87 49.3702 35.4913 47.9551C37.1125 46.5401 38.1662 44.5855 38.4571 42.4533L42.6569 11.65H44.5C45.0039 11.65 45.4872 11.4498 45.8435 11.0935C46.1998 10.7372 46.4 10.2539 46.4 9.75C46.4 9.24609 46.1998 8.76282 45.8435 8.4065C45.4872 8.05018 45.0039 7.85 44.5 7.85H30.6484C30.6098 6.00802 29.8613 4.24968 28.5558 2.94419C27.2149 1.6033 25.3963 0.85 23.5 0.85C21.6037 0.85 19.7851 1.6033 18.4442 2.94419C17.1387 4.24968 16.3902 6.00802 16.3516 7.85H2.5C1.99609 7.85 1.51282 8.05018 1.1565 8.4065C0.800178 8.76282 0.6 9.24609 0.6 9.75C0.6 10.2539 0.800178 10.7372 1.1565 11.0935C1.51282 11.4498 1.99609 11.65 2.5 11.65H4.34306L8.54288 42.4533ZM29.6391 46.35H29.639H17.361C16.1281 46.3498 14.937 45.903 14.0081 45.0923C13.0792 44.2816 12.4755 43.1618 12.3086 41.9402C12.3086 41.9402 12.3086 41.9402 12.3086 41.9402L8.17735 11.65H38.8227L34.6949 41.9402C34.5279 43.1624 33.9237 44.2826 32.9941 45.0934C32.0645 45.9042 30.8726 46.3507 29.6391 46.35ZM25.8688 5.63119C26.4616 6.22399 26.8093 7.01609 26.8466 7.85H20.1534C20.1907 7.01609 20.5384 6.22399 21.1312 5.63119C21.7594 5.00295 22.6115 4.65 23.5 4.65C24.3885 4.65 25.2406 5.00295 25.8688 5.63119ZM20.15 20.25C20.15 19.7461 19.9498 19.2628 19.5935 18.9065C19.2372 18.5502 18.7539 18.35 18.25 18.35C17.7461 18.35 17.2628 18.5502 16.9065 18.9065C16.5502 19.2628 16.35 19.7461 16.35 20.25V37.75C16.35 38.2539 16.5502 38.7372 16.9065 39.0935C17.2628 39.4498 17.7461 39.65 18.25 39.65C18.7539 39.65 19.2372 39.4498 19.5935 39.0935C19.9498 38.7372 20.15 38.2539 20.15 37.75V20.25ZM30.0935 18.9065C29.7372 18.5502 29.2539 18.35 28.75 18.35C28.2461 18.35 27.7628 18.5502 27.4065 18.9065C27.0502 19.2628 26.85 19.7461 26.85 20.25V37.75C26.85 38.2539 27.0502 38.7372 27.4065 39.0935C27.7628 39.4498 28.2461 39.65 28.75 39.65C29.2539 39.65 29.7372 39.4498 30.0935 39.0935C30.4498 38.7372 30.65 38.2539 30.65 37.75V20.25C30.65 19.7461 30.4498 19.2628 30.0935 18.9065Z"
      fill="white"
      stroke={color}
      strokeWidth="0.3"
    />
  </svg>
);

export const DeleteCircleIcon = ({ width = "32px", height = "32px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="0.5" y="0.5" width="31" height="31" rx="15.5" fill="white" />
    <rect x="0.5" y="0.5" width="31" height="31" rx="15.5" stroke="#F03D3E" />
    <path
      d="M21.3383 22.0543L21.2887 22.0475L21.3383 22.0543L22.8386 11.05H23.5C23.679 11.05 23.8507 10.9789 23.9773 10.8523C24.1039 10.7257 24.175 10.554 24.175 10.375C24.175 10.196 24.1039 10.0243 23.9773 9.8977C23.8507 9.77112 23.679 9.7 23.5 9.7H18.5495C18.5366 9.0418 18.2695 8.4133 17.8031 7.94688C17.3249 7.46866 16.6763 7.2 16 7.2C15.3237 7.2 14.6751 7.46866 14.1969 7.94688C13.7305 8.4133 13.4634 9.0418 13.4505 9.7H8.5C8.32098 9.7 8.14929 9.77112 8.0227 9.8977C7.89612 10.0243 7.825 10.196 7.825 10.375C7.825 10.554 7.89612 10.7257 8.0227 10.8523C8.14929 10.9789 8.32098 11.05 8.5 11.05H9.16135L10.6617 22.0543L10.7113 22.0475L10.6617 22.0543C10.7655 22.8149 11.1414 23.5122 11.7197 24.017C12.2981 24.5218 13.0398 24.8 13.8075 24.8H18.1925C18.9602 24.8 19.7019 24.5218 20.2803 24.017C20.8586 23.5122 21.2345 22.8149 21.3383 22.0543ZM16.8485 8.90147C17.0618 9.1147 17.1865 9.39992 17.199 9.7H14.801C14.8135 9.39992 14.9382 9.1147 15.1515 8.90147C15.3765 8.67643 15.6817 8.55 16 8.55C16.3183 8.55 16.6235 8.67643 16.8485 8.90147ZM14.8 14.125C14.8 13.946 14.7289 13.7743 14.6023 13.6477C14.4757 13.5211 14.304 13.45 14.125 13.45C13.946 13.45 13.7743 13.5211 13.6477 13.6477C13.5211 13.7743 13.45 13.946 13.45 14.125V20.375C13.45 20.554 13.5211 20.7257 13.6477 20.8523C13.7743 20.9789 13.946 21.05 14.125 21.05C14.304 21.05 14.4757 20.9789 14.6023 20.8523C14.7289 20.7257 14.8 20.554 14.8 20.375V14.125ZM18.3523 13.6477C18.2257 13.5211 18.054 13.45 17.875 13.45C17.696 13.45 17.5243 13.5211 17.3977 13.6477C17.2711 13.7743 17.2 13.946 17.2 14.125V20.375C17.2 20.554 17.2711 20.7257 17.3977 20.8523C17.5243 20.9789 17.696 21.05 17.875 21.05C18.054 21.05 18.2257 20.9789 18.3523 20.8523C18.4789 20.7257 18.55 20.554 18.55 20.375V14.125C18.55 13.946 18.4789 13.7743 18.3523 13.6477ZM12.6077 22.9999C12.2753 22.7098 12.0593 22.3091 11.9995 21.872C11.9995 21.872 11.9995 21.872 11.9995 21.872L10.5235 11.05H21.4765L20.0017 21.872C20.0017 21.872 20.0017 21.872 20.0017 21.872C19.942 22.3093 19.7258 22.7102 19.3931 23.0003C19.0605 23.2905 18.6339 23.4502 18.1925 23.45H13.8075C13.3663 23.4499 12.9401 23.2901 12.6077 22.9999Z"
      fill="#F03D3E"
      stroke="#F03D3E"
      strokeWidth="0.1"
    />
  </svg>
);

export const UndoIcon = ({ width = "32px", height = "32px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width={width} height={height} rx="16" fill="white" />
    <path
      d="M12.6666 21.8333C12.4305 21.8333 12.2327 21.7533 12.0733 21.5933C11.9138 21.4333 11.8338 21.2356 11.8333 21C11.8327 20.7644 11.9127 20.5667 12.0733 20.4067C12.2338 20.2467 12.4316 20.1667 12.6666 20.1667H17.75C18.625 20.1667 19.3855 19.8889 20.0316 19.3333C20.6777 18.7778 21.0005 18.0833 21 17.25C20.9994 16.4167 20.6766 15.7222 20.0316 15.1667C19.3866 14.6111 18.6261 14.3333 17.75 14.3333H12.5L14.0833 15.9167C14.2361 16.0694 14.3125 16.2639 14.3125 16.5C14.3125 16.7361 14.2361 16.9306 14.0833 17.0833C13.9305 17.2361 13.7361 17.3125 13.5 17.3125C13.2638 17.3125 13.0694 17.2361 12.9166 17.0833L9.91662 14.0833C9.83328 14 9.77412 13.9097 9.73912 13.8125C9.70412 13.7153 9.68717 13.6111 9.68828 13.5C9.68939 13.3889 9.70689 13.2847 9.74078 13.1875C9.77467 13.0903 9.83328 13 9.91662 12.9167L12.9166 9.91667C13.0694 9.76389 13.2638 9.6875 13.5 9.6875C13.7361 9.6875 13.9305 9.76389 14.0833 9.91667C14.2361 10.0694 14.3125 10.2639 14.3125 10.5C14.3125 10.7361 14.2361 10.9306 14.0833 11.0833L12.5 12.6667H17.75C19.0972 12.6667 20.2536 13.1042 21.2191 13.9792C22.1847 14.8542 22.6672 15.9444 22.6666 17.25C22.6661 18.5556 22.1836 19.6458 21.2191 20.5208C20.2547 21.3958 19.0983 21.8333 17.75 21.8333H12.6666Z"
      fill="black"
      fillOpacity="0.87"
    />
  </svg>
);

export const DownloadIcon = ({
  width = "16px",
  height = "17px",
  color = "white",
  ...props
}) => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.99984 10.8827C7.91095 10.8827 7.82762 10.8689 7.74984 10.8414C7.67206 10.8138 7.59984 10.7665 7.53317 10.6994L5.13317 8.29935C4.99984 8.16602 4.93584 8.01046 4.94117 7.83268C4.9465 7.65491 5.0105 7.49935 5.13317 7.36602C5.2665 7.23268 5.42495 7.16335 5.6085 7.15802C5.79206 7.15268 5.95028 7.21646 6.08317 7.34935L7.33317 8.59935V3.83268C7.33317 3.6438 7.39717 3.48557 7.52517 3.35802C7.65317 3.23046 7.81139 3.16646 7.99984 3.16602C8.18828 3.16557 8.34673 3.22957 8.47517 3.35802C8.60362 3.48646 8.66739 3.64468 8.6665 3.83268V8.59935L9.9165 7.34935C10.0498 7.21602 10.2083 7.15202 10.3918 7.15735C10.5754 7.16268 10.7336 7.23224 10.8665 7.36602C10.9887 7.49935 11.0527 7.65491 11.0585 7.83268C11.0643 8.01046 11.0003 8.16602 10.8665 8.29935L8.4665 10.6994C8.39984 10.766 8.32762 10.8134 8.24984 10.8414C8.17206 10.8694 8.08873 10.8831 7.99984 10.8827ZM3.99984 13.8327C3.63317 13.8327 3.31939 13.7022 3.0585 13.4414C2.79761 13.1805 2.66695 12.8665 2.6665 12.4994V11.166C2.6665 10.9771 2.7305 10.8189 2.8585 10.6914C2.9865 10.5638 3.14473 10.4998 3.33317 10.4994C3.52162 10.4989 3.68006 10.5629 3.8085 10.6914C3.93695 10.8198 4.00073 10.978 3.99984 11.166V12.4994H11.9998V11.166C11.9998 10.9771 12.0638 10.8189 12.1918 10.6914C12.3198 10.5638 12.4781 10.4998 12.6665 10.4994C12.8549 10.4989 13.0134 10.5629 13.1418 10.6914C13.2703 10.8198 13.3341 10.978 13.3332 11.166V12.4994C13.3332 12.866 13.2027 13.18 12.9418 13.4414C12.6809 13.7027 12.3669 13.8331 11.9998 13.8327H3.99984Z"
      fill={color}
    />
  </svg>
);

export const LogoutIcon = ({ width = "16px", height = "16px", color = "white", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M40.1667 37.8333L49.5 28.5M49.5 28.5L40.1667 19.1667M49.5 28.5H16.8333M30.8333 37.8333V40.1667C30.8333 42.0232 30.0958 43.8037 28.7831 45.1164C27.4703 46.4292 25.6898 47.1667 23.8333 47.1667H14.5C12.6435 47.1667 10.863 46.4292 9.55025 45.1164C8.2375 43.8037 7.5 42.0232 7.5 40.1667V16.8333C7.5 14.9768 8.2375 13.1964 9.55025 11.8836C10.863 10.5708 12.6435 9.83334 14.5 9.83334H23.8333C25.6898 9.83334 27.4703 10.5708 28.7831 11.8836C30.0958 13.1964 30.8333 14.9768 30.8333 16.8333V19.1667" stroke={color} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const SearchIcon = ({ width = "20px", height = "20px", color = "black", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5001 9.57926C17.5001 11.145 17.0358 12.6756 16.1659 13.9775C15.296 15.2794 14.0596 16.2941 12.613 16.8933C11.1664 17.4925 9.57464 17.6493 8.03895 17.3438C6.50327 17.0383 5.09266 16.2844 3.98549 15.1772C2.87832 14.07 2.12433 12.6594 1.81887 11.1237C1.5134 9.58805 1.67018 7.99627 2.26937 6.54969C2.86857 5.10311 3.88326 3.86669 5.18515 2.9968C6.48704 2.1269 8.01765 1.6626 9.58342 1.6626C11.683 1.6626 13.6967 2.49667 15.1813 3.98134C16.666 5.466 17.5001 7.47963 17.5001 9.57926V9.57926Z" stroke="black" strokeOpacity="0.6" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18.3334 18.3293L16.6667 16.6626" stroke={color} strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const EditIcon = ({ width = "16px", height = "16px", color = "#204464", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.8398 2.3994L3.36647 8.19273C3.13647 8.46578 2.98242 8.79458 2.9198 9.14606L2.67314 11.3067C2.6408 11.4747 2.65132 11.6482 2.70373 11.811C2.75614 11.9739 2.84874 12.1209 2.97299 12.2385C3.09724 12.3561 3.24912 12.4405 3.4146 12.4839C3.58009 12.5273 3.75384 12.5282 3.9198 12.4867L6.06647 12.1201C6.41603 12.0433 6.73707 11.8701 6.99314 11.6201L12.4665 5.82673C13.4131 4.82673 13.8398 3.68673 12.3665 2.2934C10.8998 0.91273 9.78647 1.3994 8.8398 2.3994Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.92676 3.36719C8.06652 4.26109 8.49916 5.08328 9.15677 5.70469C9.81437 6.32609 10.6597 6.71154 11.5601 6.80052" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2 14.6667H14" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const EEmailIcon = ({ width = "16px", height = "16px", color = "#204464", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14 8.00011C14 6.74643 13.6073 5.52423 12.8771 4.50517C12.1469 3.48611 11.1158 2.72139 9.92861 2.31841C8.74146 1.91543 7.4579 1.89444 6.2582 2.25839C5.05851 2.62233 4.00295 3.35293 3.23978 4.34756C2.47661 5.3422 2.04418 6.5509 2.00321 7.80392C1.96223 9.05693 2.31479 10.2913 3.01135 11.3337C3.70791 12.376 4.71349 13.174 5.88684 13.6156C7.0602 14.0571 8.34239 14.12 9.55333 13.7954" stroke={color} strokeOpacity="0.87" strokeWidth="1.33333" strokeLinecap="round" />
    <path d="M8.00016 10.6666C9.47292 10.6666 10.6668 9.47274 10.6668 7.99998C10.6668 6.52722 9.47292 5.33331 8.00016 5.33331C6.5274 5.33331 5.3335 6.52722 5.3335 7.99998C5.3335 9.47274 6.5274 10.6666 8.00016 10.6666Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.33333" />
    <path d="M10.6665 6V9C10.6665 9.44203 10.8421 9.86595 11.1547 10.1785C11.4672 10.4911 11.8911 10.6667 12.3332 10.6667C12.7752 10.6667 13.1991 10.4911 13.5117 10.1785C13.8242 9.86595 13.9998 9.44203 13.9998 9V8" stroke={color} strokeOpacity="0.87" strokeWidth="1.33333" strokeLinecap="round" />
  </svg>
);

export const CheckDeleteIcon = ({ width = "16px", height = "16px", color = "#204464", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 3H9C9 2.73478 8.89464 2.48043 8.70711 2.29289C8.51957 2.10536 8.26522 2 8 2C7.73478 2 7.48043 2.10536 7.29289 2.29289C7.10536 2.48043 7 2.73478 7 3ZM6 3C6 2.46957 6.21071 1.96086 6.58579 1.58579C6.96086 1.21071 7.46957 1 8 1C8.53043 1 9.03914 1.21071 9.41421 1.58579C9.78929 1.96086 10 2.46957 10 3H14C14.1326 3 14.2598 3.05268 14.3536 3.14645C14.4473 3.24021 14.5 3.36739 14.5 3.5C14.5 3.63261 14.4473 3.75979 14.3536 3.85355C14.2598 3.94732 14.1326 4 14 4H13.436L12.231 12.838C12.1493 13.4369 11.8533 13.986 11.3979 14.3835C10.9425 14.781 10.3585 15 9.754 15H6.246C5.64152 15 5.05751 14.781 4.6021 14.3835C4.14669 13.986 3.85073 13.4369 3.769 12.838L2.564 4H2C1.86739 4 1.74021 3.94732 1.64645 3.85355C1.55268 3.75979 1.5 3.63261 1.5 3.5C1.5 3.36739 1.55268 3.24021 1.64645 3.14645C1.74021 3.05268 1.86739 3 2 3H6ZM7 6.5C7 6.36739 6.94732 6.24021 6.85355 6.14645C6.75979 6.05268 6.63261 6 6.5 6C6.36739 6 6.24021 6.05268 6.14645 6.14645C6.05268 6.24021 6 6.36739 6 6.5V11.5C6 11.6326 6.05268 11.7598 6.14645 11.8536C6.24021 11.9473 6.36739 12 6.5 12C6.63261 12 6.75979 11.9473 6.85355 11.8536C6.94732 11.7598 7 11.6326 7 11.5V6.5ZM9.5 6C9.63261 6 9.75979 6.05268 9.85355 6.14645C9.94732 6.24021 10 6.36739 10 6.5V11.5C10 11.6326 9.94732 11.7598 9.85355 11.8536C9.75979 11.9473 9.63261 12 9.5 12C9.36739 12 9.24021 11.9473 9.14645 11.8536C9.05268 11.7598 9 11.6326 9 11.5V6.5C9 6.36739 9.05268 6.24021 9.14645 6.14645C9.24021 6.05268 9.36739 6 9.5 6ZM4.76 12.703C4.80908 13.0623 4.98664 13.3916 5.25984 13.6301C5.53304 13.8685 5.88337 14 6.246 14H9.754C10.1168 14.0002 10.4674 13.8689 10.7408 13.6304C11.0142 13.3919 11.1919 13.0625 11.241 12.703L12.427 4H3.573L4.76 12.703Z" fill={color} fillOpacity="0.87" />
    <path d="M12.2805 12.8448L12.231 12.838L12.2805 12.8448L13.4796 4.05H14C14.1459 4.05 14.2858 3.99205 14.3889 3.88891C14.4921 3.78576 14.55 3.64587 14.55 3.5C14.55 3.35413 14.4921 3.21424 14.3889 3.11109C14.2858 3.00795 14.1459 2.95 14 2.95H10.0494C10.0366 2.42442 9.82222 1.92308 9.44957 1.55043C9.06512 1.16598 8.54369 0.95 8 0.95C7.45631 0.95 6.93488 1.16598 6.55043 1.55043C6.17778 1.92308 5.96342 2.42442 5.95061 2.95H2C1.85413 2.95 1.71424 3.00795 1.61109 3.11109C1.50795 3.21424 1.45 3.35413 1.45 3.5C1.45 3.64587 1.50795 3.78576 1.61109 3.88891C1.71424 3.99205 1.85413 4.05 2 4.05H2.52035L3.71946 12.8448L3.769 12.838L3.71946 12.8448C3.80282 13.4557 4.1047 14.0157 4.56922 14.4211C5.03374 14.8266 5.62943 15.05 6.246 15.05H9.754C10.3706 15.05 10.9663 14.8266 11.4308 14.4211C11.8953 14.0157 12.1972 13.4557 12.2805 12.8448ZM9.75403 13.95H9.754H6.24601C5.89547 13.95 5.55681 13.8229 5.29272 13.5924C5.02863 13.3619 4.85698 13.0435 4.80954 12.6962L3.63028 4.05H12.3697L11.1915 12.6962C11.1915 12.6962 11.1915 12.6962 11.1915 12.6962C11.144 13.0437 10.9722 13.3622 10.7079 13.5927C10.4436 13.8233 10.1047 13.9502 9.75403 13.95ZM8.67175 2.32825C8.83809 2.49459 8.93637 2.71626 8.94868 2.95H7.05132C7.06363 2.71626 7.16191 2.49459 7.32825 2.32825C7.50641 2.15009 7.74804 2.05 8 2.05C8.25196 2.05 8.49359 2.15009 8.67175 2.32825ZM7.05 6.5C7.05 6.35413 6.99205 6.21424 6.88891 6.11109C6.78576 6.00795 6.64587 5.95 6.5 5.95C6.35413 5.95 6.21424 6.00795 6.11109 6.11109C6.00795 6.21424 5.95 6.35413 5.95 6.5V11.5C5.95 11.6459 6.00795 11.7858 6.11109 11.8889C6.21424 11.9921 6.35413 12.05 6.5 12.05C6.64587 12.05 6.78576 11.9921 6.88891 11.8889C6.99205 11.7858 7.05 11.6459 7.05 11.5V6.5ZM9.88891 6.11109C9.78576 6.00795 9.64587 5.95 9.5 5.95C9.35413 5.95 9.21424 6.00795 9.11109 6.11109C9.00795 6.21424 8.95 6.35413 8.95 6.5V11.5C8.95 11.6459 9.00795 11.7858 9.11109 11.8889C9.21424 11.9921 9.35413 12.05 9.5 12.05C9.64587 12.05 9.78576 11.9921 9.88891 11.8889C9.99205 11.7858 10.05 11.6459 10.05 11.5V6.5C10.05 6.35413 9.99205 6.21424 9.88891 6.11109Z" stroke={color} strokeOpacity="0.87" strokeWidth="0.1" />
  </svg>
);

export const PrintIcon = ({ width = "16px", height = "16px", color = "#204464", ...props }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.8335 4.66732H11.1668V3.33398C11.1668 2.00065 10.6668 1.33398 9.16683 1.33398H6.8335C5.3335 1.33398 4.8335 2.00065 4.8335 3.33398V4.66732Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6665 10V12.6667C10.7052 12.9383 10.6802 13.2153 10.5934 13.4756C10.5066 13.736 10.3604 13.9725 10.1664 14.1666C9.97233 14.3606 9.73578 14.5068 9.47545 14.5936C9.21512 14.6804 8.93816 14.7054 8.66649 14.6667H7.33316C7.06149 14.7054 6.78452 14.6804 6.52419 14.5936C6.26386 14.5068 6.02731 14.3606 5.83327 14.1666C5.63922 13.9725 5.49301 13.736 5.40621 13.4756C5.3194 13.2153 5.29439 12.9383 5.33316 12.6667V10H10.6665Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.0001 6.66616V9.9995C14.0389 10.2712 14.0139 10.5481 13.9271 10.8085C13.8403 11.0688 13.6941 11.3053 13.5 11.4994C13.306 11.6934 13.0694 11.8396 12.8091 11.9264C12.5488 12.0132 12.2718 12.0383 12.0001 11.9995H10.6668V9.9995H5.33348V11.9995H4.00015C3.72848 12.0383 3.45151 12.0132 3.19118 11.9264C2.93085 11.8396 2.6943 11.6934 2.50026 11.4994C2.30621 11.3053 2.16 11.0688 2.0732 10.8085C1.9864 10.5481 1.96139 10.2712 2.00015 9.9995V6.66616C1.96139 6.39449 1.9864 6.11753 2.0732 5.8572C2.16 5.59687 2.30621 5.36032 2.50026 5.16627C2.6943 4.97223 2.93085 4.82602 3.19118 4.73921C3.45151 4.65241 3.72848 4.6274 4.00015 4.66616H12.0001C12.2718 4.6274 12.5488 4.65241 12.8091 4.73921C13.0694 4.82602 13.306 4.97223 13.5 5.16627C13.6941 5.36032 13.8403 5.59687 13.9271 5.8572C14.0139 6.11753 14.0389 6.39449 14.0001 6.66616V6.66616Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.3332 10H4.6665" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.6665 7.33398H6.6665" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const VoiceIcon = ({ width = "16px", height = "16px", color = "#204464" }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 9.66667L8.88533 11.552M8.88533 9.66667L7 11.552M6.48533 2H5C4.60218 2 4.22064 2.15804 3.93934 2.43934C3.65804 2.72064 3.5 3.10218 3.5 3.5V12.5C3.5 12.8978 3.65804 13.2794 3.93934 13.5607C4.22064 13.842 4.60218 14 5 14H11C11.3978 14 11.7794 13.842 12.0607 13.5607C12.342 13.2794 12.5 12.8978 12.5 12.5V8M6.48533 2C7.314 2 8 2.67133 8 3.5V5C8 5.39782 8.15804 5.77936 8.43934 6.06066C8.72064 6.34196 9.10218 6.5 9.5 6.5H11C11.3978 6.5 11.7794 6.65804 12.0607 6.93934C12.342 7.22064 12.5 7.60218 12.5 8M6.48533 2C8.94533 2 12.5 5.57333 12.5 8" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ClearedIcon = ({ width = "16px", height = "16px", color = "#204464" }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.00016 14.6665H10.0002C13.3335 14.6665 14.6668 13.3331 14.6668 9.9998V5.9998C14.6668 2.66646 13.3335 1.33313 10.0002 1.33313H6.00016C2.66683 1.33313 1.3335 2.66646 1.3335 5.9998V9.9998C1.3335 13.3331 2.66683 14.6665 6.00016 14.6665Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.1665 7.99995L7.05317 9.88661L10.8332 6.11328" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const DetailIcon = ({ width = "16px", height = "16px", color = "#204464" }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.3866 7.99995C10.3866 8.47199 10.2466 8.93343 9.98439 9.32591C9.72214 9.7184 9.34939 10.0243 8.91329 10.2049C8.47718 10.3856 7.9973 10.4328 7.53433 10.3408C7.07137 10.2487 6.6461 10.0214 6.31232 9.68758C5.97854 9.3538 5.75123 8.92853 5.65914 8.46557C5.56705 8.0026 5.61432 7.52272 5.79496 7.08661C5.9756 6.65051 6.2815 6.27776 6.67399 6.01551C7.06647 5.75326 7.52791 5.61328 7.99995 5.61328C8.31347 5.61293 8.62398 5.67443 8.9137 5.79424C9.20342 5.91406 9.46667 6.08985 9.68836 6.31154C9.91005 6.53323 10.0858 6.79647 10.2057 7.0862C10.3255 7.37592 10.387 7.68643 10.3866 7.99995V7.99995Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.99986 13.5138C10.3532 13.5138 12.5465 12.1271 14.0732 9.72714C14.3683 9.19828 14.5232 8.60274 14.5232 7.99714C14.5232 7.39153 14.3683 6.79599 14.0732 6.26714C12.5432 3.86714 10.3532 2.48047 7.99986 2.48047C5.64653 2.48047 3.4532 3.86714 1.92653 6.26714C1.63146 6.79599 1.47656 7.39153 1.47656 7.99714C1.47656 8.60274 1.63146 9.19828 1.92653 9.72714C3.4532 12.1271 5.64653 13.5138 7.99986 13.5138Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ClearedCheckIcon = ({ width = "57px", height = "57px", color = "white" }) => (
  <svg width={width} height={height} viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.9302 27.1459L6.87016 23.0859L2.8335 27.1459" stroke={color} strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M46.0693 29.8535L50.1293 33.9135L54.1893 29.8535" stroke={color} strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M50.1065 33.9154V28.502C50.1059 24.0255 48.7151 19.6597 46.1263 16.0076C43.5376 12.3556 39.8786 9.59752 35.6549 8.11448C31.4312 6.63144 26.8512 6.49661 22.5475 7.7286C18.2439 8.96059 14.429 11.4986 11.6299 14.992" stroke={color} strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.89404 23.0859V28.4993C6.89473 32.9758 8.28552 37.3416 10.8743 40.9937C13.463 44.6457 17.122 47.4038 21.3457 48.8868C25.5694 50.3699 30.1494 50.5047 34.4531 49.2727C38.7567 48.0407 42.5716 45.5027 45.3707 42.0093" stroke={color} strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const UnsavedIcon = ({ width = "57px", height = "57px", color = "#204464" }) => (
  <svg width={width} height={height} viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.8965 35.1032L35.1032 21.8965" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M35.1032 35.1032L21.8965 21.8965" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M21.4998 51.8334H35.4998C47.1665 51.8334 51.8332 47.1668 51.8332 35.5001V21.5001C51.8332 9.83342 47.1665 5.16675 35.4998 5.16675H21.4998C9.83317 5.16675 5.1665 9.83342 5.1665 21.5001V35.5001C5.1665 47.1668 9.83317 51.8334 21.4998 51.8334Z" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const DownloadCheckIcon = ({ width = "57px", height = "57px", color = "#FFFFFF" }) => (
  <svg width={width} height={height} viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.5003 51.8327H35.5003C47.167 51.8327 51.8337 47.166 51.8337 35.4994V21.4994C51.8337 9.83268 47.167 5.16602 35.5003 5.16602H21.5003C9.83366 5.16602 5.16699 9.83268 5.16699 21.4994V35.4994C5.16699 47.166 9.83366 51.8327 21.5003 51.8327Z" stroke={color} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18.583 28.4998L25.1863 35.1032L38.4163 21.8965" stroke={color} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const GeneratingCheckIcon = ({ width = "140px", height = "140px" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 141 140"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      animation: 'rotate 1s linear infinite',
    }}
  >
    <style>
      {`
          @keyframes rotate {
            from {
              transform: rotate(360deg);
            }
            to {
              transform: rotate(0deg);
            }
          }
        `}
    </style>
    <g clipPath="url(#paint0_angular_1249_21865_clip_path)" data-figma-skip-parse="true">
      <g transform="matrix(0 0.07 -0.07 0 70.5 70)">
        <foreignObject x="-1066.67" y="-1066.67" width="2133.33" height="2133.33">
          <div xmlns="http://www.w3.org/1999/xhtml" style={{
            background: 'conic-gradient(from 90deg,rgba(32, 68, 100, 1) 0deg,rgba(255, 255, 255, 0.4) 360deg)',
            height: '100%',
            width: '100%',
            opacity: 1
          }}></div>
        </foreignObject>
      </g>
    </g>
    <path d="M140.5 70C140.5 108.66 109.16 140 70.5 140C31.8401 140 0.5 108.66 0.5 70C0.5 31.3401 31.8401 0 70.5 0C109.16 0 140.5 31.3401 140.5 70ZM14.5 70C14.5 100.928 39.5721 126 70.5 126C101.428 126 126.5 100.928 126.5 70C126.5 39.0721 101.428 14 70.5 14C39.5721 14 14.5 39.0721 14.5 70Z" />
    <circle cx="69.333" cy="133" r="7" fill="#204464" />
    <defs>
      <clipPath id="paint0_angular_1249_21865_clip_path">
        <path d="M140.5 70C140.5 108.66 109.16 140 70.5 140C31.8401 140 0.5 108.66 0.5 70C0.5 31.3401 31.8401 0 70.5 0C109.16 0 140.5 31.3401 140.5 70ZM14.5 70C14.5 100.928 39.5721 126 70.5 126C101.428 126 126.5 100.928 126.5 70C126.5 39.0721 101.428 14 70.5 14C39.5721 14 14.5 39.0721 14.5 70Z" />
      </clipPath>
    </defs>
  </svg>
);

export const PDFIcon = ({ width = "56px", height = "56px" }) => (
  <svg width={width} height={height} viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.1813 49.5834H37.8186C39.9846 49.5834 42.0618 48.7229 43.5934 47.1914C45.1249 45.6598 45.9853 43.5826 45.9853 41.4167V28.5134C45.9861 26.3477 45.1266 24.2704 43.596 22.7384L29.6683 8.80835C28.9099 8.05003 28.0096 7.44851 27.0187 7.03814C26.0278 6.62778 24.9658 6.41661 23.8933 6.41669H18.1813C16.0154 6.41669 13.9382 7.2771 12.4066 8.80865C10.8751 10.3402 10.0146 12.4174 10.0146 14.5834V41.4167C10.0146 43.5826 10.8751 45.6598 12.4066 47.1914C13.9382 48.7229 16.0154 49.5834 18.1813 49.5834Z" stroke="url(#paint0_linear_1249_19673)" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M27.2725 7.25671V20.4634C27.2725 21.7011 27.7641 22.888 28.6393 23.7632C29.5145 24.6384 30.7015 25.13 31.9391 25.13H45.1505" stroke="url(#paint1_linear_1249_19673)" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.917 38.5V36.1667M16.917 36.1667V31.5H19.2503C19.8692 31.5 20.4627 31.7458 20.9002 32.1834C21.3378 32.621 21.5837 33.2145 21.5837 33.8333C21.5837 34.4522 21.3378 35.0457 20.9002 35.4832C20.4627 35.9208 19.8692 36.1667 19.2503 36.1667H16.917ZM35.5837 38.5V35.5833M35.5837 35.5833V31.5H39.0837M35.5837 35.5833H39.0837M26.2503 38.5V31.5H27.417C28.3452 31.5 29.2355 31.8687 29.8919 32.5251C30.5482 33.1815 30.917 34.0717 30.917 35C30.917 35.9283 30.5482 36.8185 29.8919 37.4749C29.2355 38.1312 28.3452 38.5 27.417 38.5H26.2503Z" stroke="#204464" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <defs>
      <linearGradient id="paint0_linear_1249_19673" x1="28" y1="14.5007" x2="28" y2="30.5007" gradientUnits="userSpaceOnUse">
        <stop stopColor="#CECECE" />
        <stop offset="1" stopColor="#204464" />
      </linearGradient>
      <linearGradient id="paint1_linear_1249_19673" x1="36.2115" y1="10.6039" x2="37" y2="32.0009" gradientUnits="userSpaceOnUse">
        <stop stopColor="#CECECE" />
        <stop offset="1" stopColor="#204464" />
      </linearGradient>
    </defs>
  </svg>
);

export const CalendarIcon = ({ width = "20px", height = '20px' }) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.66699 1.66455V4.16455" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.333 1.66455V4.16455" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M2.91699 7.57446H17.0837" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M17.5 7.08073V14.1641C17.5 16.6641 16.25 18.3307 13.3333 18.3307H6.66667C3.75 18.3307 2.5 16.6641 2.5 14.1641V7.08073C2.5 4.58073 3.75 2.91406 6.66667 2.91406H13.3333C16.25 2.91406 17.5 4.58073 17.5 7.08073Z" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.0789 11.4172H13.0836" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.0789 13.918H13.0836" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.99707 11.4172H10.0018" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.99707 13.918H10.0018" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.91211 11.4172H6.91682" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.91211 13.918H6.91682" stroke="black" strokeOpacity="0.87" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const ErrorIcon = ({ width = "20px", height = "20px", color = "#F03D3E" }) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 7.5V11.6667" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.99882 17.8409H15.0496C17.9413 17.8409 19.1496 15.7743 17.7496 13.2493L15.1496 8.56592L12.6996 4.16592C11.2163 1.49092 8.78298 1.49092 7.29965 4.16592L4.84965 8.57425L2.24965 13.2576C0.84965 15.7826 2.06632 17.8493 4.94965 17.8493H9.99882V17.8409Z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.0044 14.1665H9.99658" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const BatchIcon = ({ width = "56px", height = "56px" }) => (
  <svg width={width} height={height} viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.1813 49.5833H37.8186C39.9846 49.5833 42.0618 48.7229 43.5934 47.1913C45.1249 45.6598 45.9853 43.5826 45.9853 41.4166V28.5133C45.9861 26.3476 45.1266 24.2703 43.596 22.7383L29.6683 8.80829C28.9099 8.04997 28.0096 7.44845 27.0187 7.03808C26.0278 6.62772 24.9658 6.41655 23.8933 6.41663H18.1813C16.0154 6.41663 13.9382 7.27704 12.4066 8.80859C10.8751 10.3401 10.0146 12.4174 10.0146 14.5833V41.4166C10.0146 43.5826 10.8751 45.6598 12.4066 47.1913C13.9382 48.7229 16.0154 49.5833 18.1813 49.5833Z" stroke="url(#paint0_linear_1317_4208)" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M27.272 7.25659V20.4633C27.272 21.7009 27.7636 22.8879 28.6388 23.7631C29.514 24.6383 30.701 25.1299 31.9386 25.1299H45.15" stroke="url(#paint1_linear_1317_4208)" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
    <rect x="17" y="40" width="22" height="2.5" rx="1.25" fill="#204464" />
    <rect x="16" y="35" width="24" height="2.5" rx="1.25" fill="#204464" />
    <rect x="16" y="30" width="24" height="2.5" rx="1.25" fill="#204464" />
    <defs>
      <linearGradient id="paint0_linear_1317_4208" x1="28" y1="14.5006" x2="28" y2="30.5006" gradientUnits="userSpaceOnUse">
        <stop stopColor="#CECECE" />
        <stop offset="1" stopColor="#204464" />
      </linearGradient>
      <linearGradient id="paint1_linear_1317_4208" x1="36.211" y1="10.6038" x2="36.9995" y2="32.0007" gradientUnits="userSpaceOnUse">
        <stop stopColor="#CECECE" />
        <stop offset="1" stopColor="#204464" />
      </linearGradient>
    </defs>
  </svg>

)

export const WarningIcon = ({ width = "57px", height = "57px", color = "#FFFFFF" }) => (
  <svg width={width} height={height} viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M28.5 21.5V33.1667" stroke={color} strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M28.5023 50.4544H14.36C6.26334 50.4544 2.88001 44.6677 6.80001 37.5977L14.08 24.4844L20.94 12.1644C25.0933 4.67438 31.9067 4.67438 36.06 12.1644L42.92 24.5077L50.2 37.621C54.12 44.691 50.7133 50.4777 42.64 50.4777H28.5023V50.4544Z" stroke="white" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M28.4888 40.1667H28.5098" stroke="white" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TagIcon = ({ width = "20px", height = "20px" }) => (<svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M17.5 5C17.5 5.49445 17.3534 5.9778 17.0787 6.38892C16.804 6.80005 16.4135 7.12048 15.9567 7.3097C15.4999 7.49892 14.9972 7.54842 14.5123 7.45196C14.0273 7.3555 13.5819 7.1174 13.2322 6.76777C12.8826 6.41813 12.6445 5.97268 12.548 5.48772C12.4516 5.00277 12.5011 4.50011 12.6903 4.04329C12.8795 3.58648 13.2 3.19603 13.6111 2.92133C14.0222 2.64662 14.5055 2.5 15 2.5C15.663 2.5 16.2989 2.76339 16.7678 3.23223C17.2366 3.70107 17.5 4.33696 17.5 5Z" fill="black" fillOpacity="0.6" />
  <path d="M7.5 5C7.5 5.49445 7.35338 5.9778 7.07867 6.38892C6.80397 6.80005 6.41352 7.12048 5.95671 7.3097C5.49989 7.49892 4.99723 7.54842 4.51227 7.45196C4.02732 7.3555 3.58187 7.1174 3.23223 6.76777C2.8826 6.41813 2.6445 5.97268 2.54804 5.48772C2.45157 5.00277 2.50108 4.50011 2.6903 4.04329C2.87952 3.58648 3.19995 3.19603 3.61108 2.92133C4.0222 2.64662 4.50555 2.5 5 2.5C5.66304 2.5 6.29893 2.76339 6.76777 3.23223C7.23661 3.70107 7.5 4.33696 7.5 5Z" fill="black" fillOpacity="0.6" />
  <path d="M17.5 15C17.5 15.4945 17.3534 15.9778 17.0787 16.3889C16.804 16.8 16.4135 17.1205 15.9567 17.3097C15.4999 17.4989 14.9972 17.5484 14.5123 17.452C14.0273 17.3555 13.5819 17.1174 13.2322 16.7678C12.8826 16.4181 12.6445 15.9727 12.548 15.4877C12.4516 15.0028 12.5011 14.5001 12.6903 14.0433C12.8795 13.5865 13.2 13.196 13.6111 12.9213C14.0222 12.6466 14.5055 12.5 15 12.5C15.663 12.5 16.2989 12.7634 16.7678 13.2322C17.2366 13.7011 17.5 14.337 17.5 15Z" fill="black" fillOpacity="0.6" />
  <path d="M7.5 15C7.5 15.4945 7.35338 15.9778 7.07867 16.3889C6.80397 16.8 6.41352 17.1205 5.95671 17.3097C5.49989 17.4989 4.99723 17.5484 4.51227 17.452C4.02732 17.3555 3.58187 17.1174 3.23223 16.7678C2.8826 16.4181 2.6445 15.9727 2.54804 15.4877C2.45157 15.0028 2.50108 14.5001 2.6903 14.0433C2.87952 13.5865 3.19995 13.196 3.61108 12.9213C4.0222 12.6466 4.50555 12.5 5 12.5C5.66304 12.5 6.29893 12.7634 6.76777 13.2322C7.23661 13.7011 7.5 14.337 7.5 15Z" fill="black" fillOpacity="0.6" />
</svg>
)

export const UserIcon = ({ width = '16px', height = '16px', color = '#204464' }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.08002 8.52C8.0269 8.51333 7.97315 8.51333 7.92002 8.52C7.34739 8.49904 6.80588 8.25414 6.41197 7.83799C6.01806 7.42185 5.80324 6.86771 5.81372 6.29479C5.8242 5.72188 6.05915 5.17597 6.46802 4.77451C6.87689 4.37306 7.42701 4.14813 8.00002 4.14813C8.57303 4.14813 9.12315 4.37306 9.53202 4.77451C9.94089 5.17597 10.1758 5.72188 10.1863 6.29479C10.1968 6.86771 9.98199 7.42185 9.58808 7.83799C9.19417 8.25414 8.65265 8.49904 8.08002 8.52V8.52Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.4934 12.92C11.2682 14.0461 9.66407 14.6696 8.00005 14.6667C6.33602 14.6696 4.73187 14.0461 3.50671 12.92C3.5649 12.5642 3.7008 12.2256 3.90476 11.9283C4.10871 11.631 4.3757 11.3824 4.68671 11.2C5.68928 10.6044 6.83388 10.29 8.00005 10.29C9.16622 10.29 10.3108 10.6044 11.3134 11.2C11.6244 11.3824 11.8914 11.631 12.0953 11.9283C12.2993 12.2256 12.4352 12.5642 12.4934 12.92V12.92Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.6667 7.99998C14.6667 9.31852 14.2757 10.6075 13.5432 11.7038C12.8106 12.8001 11.7694 13.6546 10.5513 14.1592C9.33309 14.6638 7.99265 14.7958 6.69944 14.5385C5.40624 14.2813 4.21835 13.6464 3.286 12.714C2.35365 11.7817 1.71871 10.5938 1.46148 9.30058C1.20424 8.00737 1.33626 6.66693 1.84085 5.44876C2.34543 4.23058 3.19991 3.18939 4.29624 2.45685C5.39257 1.72431 6.6815 1.33331 8.00004 1.33331C9.76815 1.33331 11.4638 2.03569 12.7141 3.28593C13.9643 4.53618 14.6667 6.23187 14.6667 7.99998Z" stroke={color} strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const TrailDaysIcon = ({ width = '16px', height = "16px", color = "#204464" }) => (
  <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.33337 1.33331V3.33331" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6666 1.33331V3.33331" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6667 2.33331C12.8867 2.45331 14 3.29998 14 6.43331V10.5533C14 13.3 13.3333 14.6733 10 14.6733H6C2.66667 14.6733 2 13.3 2 10.5533V6.43331C2 3.29998 3.11333 2.45998 5.33333 2.33331H10.6667Z" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.8333 11.7333H2.16663" stroke={color} strokeOpacity="0.87" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8.00004 5.50003C7.81248 5.4765 7.62207 5.49275 7.4412 5.5477C7.26033 5.60266 7.09307 5.6951 6.95031 5.819C6.80754 5.94289 6.69247 6.09547 6.6126 6.2668C6.53272 6.43813 6.48983 6.62436 6.48671 6.81336C6.48294 6.99636 6.5256 7.17732 6.6107 7.33937C6.6958 7.50142 6.82058 7.63926 6.97337 7.74003C6.77964 7.8461 6.618 8.00233 6.5054 8.19234C6.3928 8.38235 6.33338 8.59916 6.33337 8.82003C6.33337 9.64669 6.96671 10.16 8.00004 10.16C9.03337 10.16 9.66671 9.64669 9.66671 8.82003C9.66764 8.59816 9.60788 8.38025 9.4939 8.1899C9.37992 7.99954 9.21606 7.84397 9.02004 7.74003C9.17328 7.63971 9.2984 7.50197 9.38356 7.33981C9.46872 7.17766 9.5111 6.99646 9.50671 6.81336C9.50442 6.62469 9.46231 6.43864 9.38315 6.26737C9.30398 6.0961 9.18953 5.94348 9.0473 5.8195C8.90507 5.69552 8.73826 5.60297 8.55779 5.54792C8.37732 5.49287 8.18726 5.47655 8.00004 5.50003ZM8.00004 7.39336C7.92485 7.40156 7.84878 7.39391 7.77673 7.37091C7.70467 7.34791 7.63824 7.31007 7.58171 7.25982C7.52518 7.20957 7.47981 7.14803 7.44852 7.07917C7.41723 7.01031 7.40071 6.93566 7.40004 6.86003C7.40039 6.78457 7.41675 6.71005 7.44804 6.64138C7.47932 6.57271 7.52483 6.51147 7.58154 6.46169C7.63825 6.41191 7.70487 6.37473 7.77702 6.35261C7.84916 6.33049 7.92517 6.32393 8.00004 6.33336C8.07491 6.32393 8.15092 6.33049 8.22307 6.35261C8.29521 6.37473 8.36184 6.41191 8.41855 6.46169C8.47526 6.51147 8.52076 6.57271 8.55204 6.64138C8.58333 6.71005 8.59969 6.78457 8.60004 6.86003C8.59937 6.93566 8.58286 7.01031 8.55156 7.07917C8.52027 7.14803 8.4749 7.20957 8.41837 7.25982C8.36184 7.31007 8.29541 7.34791 8.22336 7.37091C8.1513 7.39391 8.07523 7.40156 8.00004 7.39336ZM8.00004 9.33336C7.56004 9.33336 7.24004 9.11336 7.24004 8.71336C7.24004 8.31336 7.56004 8.10003 8.00004 8.10003C8.44004 8.10003 8.76004 8.32003 8.76004 8.71336C8.76004 9.10669 8.44004 9.33336 8.00004 9.33336Z" fill={color} fillOpacity="0.87" />
  </svg>
)

export const AttachmentPDFIcon = (width = '14px', height = '14px') => (
  <svg width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.08325 10.5007V9.04232M4.08325 9.04232V8.16732C4.08325 7.89257 4.08325 7.7549 4.17309 7.66915C4.26292 7.58398 4.407 7.58398 4.69575 7.58398H5.10409C5.527 7.58398 5.87 7.91065 5.87 8.31315C5.87 8.71565 5.527 9.04232 5.10409 9.04232H4.08325ZM12.2499 7.58398H11.4846C11.0033 7.58398 10.7624 7.58398 10.6131 7.72632C10.4638 7.86865 10.4638 8.0979 10.4638 8.5564V9.04232M10.4638 9.04232V10.5007M10.4638 9.04232H11.7395M9.18742 9.04232C9.18742 9.84732 8.502 10.5007 7.65617 10.5007C7.46542 10.5007 7.37034 10.5007 7.29859 10.4616C7.12884 10.3682 7.14575 10.1787 7.14575 10.0147V8.0699C7.14575 7.90598 7.12825 7.7164 7.29859 7.62307C7.36975 7.58398 7.46542 7.58398 7.65617 7.58398C8.502 7.58398 9.18742 8.23732 9.18742 9.04232Z" stroke="black" strokeOpacity="0.6" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8.75 12.8337H6.25742C4.35575 12.8337 3.40375 12.8337 2.74342 12.3682C2.5555 12.2364 2.38752 12.0782 2.24467 11.8986C1.75 11.2767 1.75 10.3819 1.75 8.59108V7.10649C1.75 5.37808 1.75 4.51358 2.02358 3.82349C2.46342 2.71341 3.39325 1.83841 4.57275 1.42424C5.30542 1.16699 6.223 1.16699 8.0605 1.16699C9.10933 1.16699 9.63433 1.16699 10.0532 1.31399C10.7269 1.55083 11.2583 2.05074 11.5098 2.68483C11.6667 3.07916 11.6667 3.57324 11.6667 4.56083V5.83366" stroke="black" strokeOpacity="0.6" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M1.75 7.00033C1.75 6.48468 1.95484 5.99015 2.31946 5.62553C2.68408 5.26092 3.1786 5.05608 3.69425 5.05608C4.08275 5.05608 4.54067 5.12374 4.91808 5.02283C5.083 4.97844 5.23334 4.89149 5.35405 4.77068C5.47476 4.64987 5.56159 4.49944 5.60583 4.33449C5.70675 3.95708 5.63908 3.49916 5.63908 3.11066C5.63924 2.59511 5.84415 2.10074 6.20875 1.73624C6.57335 1.37175 7.06779 1.16699 7.58333 1.16699" stroke="black" strokeOpacity="0.6" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

export const DocumentIcon = ({width = "20px", height = '20px', color= 'black'}) => (
  <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.3334 8.33366V12.5003C18.3334 16.667 16.6667 18.3337 12.5001 18.3337H7.50008C3.33341 18.3337 1.66675 16.667 1.66675 12.5003V7.50033C1.66675 3.33366 3.33341 1.66699 7.50008 1.66699H11.6667" stroke={color} strokeOpacity="0.6" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18.3334 8.33366H15.0001C12.5001 8.33366 11.6667 7.50033 11.6667 5.00033V1.66699L18.3334 8.33366Z" stroke={color} stroke-opacity="0.6" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)
