import React, { useState } from 'react';
import { Box, Typography, Select, MenuItem, Button, ListItemText, Checkbox, Divider } from '@mui/material';
import CallMadeIcon from '@mui/icons-material/CallMade';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { CustomTable } from '../../components/table/CustomTable';
import { styles } from './styles';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, AreaChart, Area, Tooltip } from 'recharts';
import { CustomButton } from '../../components/buttons/CustomButton';

// Add this data array before the component
const dailyData = [
  { name: 'Tue', value: 8 },
  { name: 'Wed', value: 4 },
  { name: 'Thu', value: 6 },
  { name: 'Fri', value: 2 },
  { name: 'Sun', value: 10 },
  { name: 'Sat', value: 4 },
  { name: '<PERSON>', value: 3 },
];

const weeklyData = [
  { name: 'Week 1', value: 15 },
  { name: 'Week 2', value: 32 },
  { name: 'Week 3', value: 13 },
  { name: 'Week 4', value: 24 },
];

const monthlyData = [
  { name: 'Jan', value: 1 },
  { name: 'Feb', value: 2 },
  { name: 'Mar', value: 5 },
  { name: 'Apr', value: 3 },
  { name: 'May', value: 2 },
  { name: 'Jun', value: 1 },
  { name: 'Jul', value: 10 },
  { name: 'Aug', value: 23 },
  { name: 'Sep', value: 13 },
  { name: 'Oct', value: 12 },
  { name: 'Nov', value: 11 },
  { name: 'Dec', value: 3 },
];
const yearlyData = [
  { name: '2022', value: 100 },
  { name: '2023', value: 200 },
  { name: '2024', value: 500 },
  { name: '2025', value: 300 },
];
const mockRecentChecks = [
  {
    no: 1,
    payeeName: 'Abraham Sabel',
    amount: 1000,
    status: 'Printed',
  },
  {
    no: 2,
    payeeName: 'Abraham Sabel',
    amount: 1000,
    status: 'Draft',
  },
  {
    no: 3,
    payeeName: 'Abraham Sabel',
    amount: 1000,
    status: 'Printed',
  },
]
const Dashboard = () => {
  const [selectedData, setSelectedData] = useState('Total Expenses');
  const [selectedChart, setSelectedChart] = useState('daily');
  const [profile, setProfile] = useState('');
  const [recentChecks, setRecentChecks] = useState(mockRecentChecks);


  const handleDataChange = (event) => {
    setSelectedData(event.target.value);
  };

  const handleProfileChange = (event) => {
    setProfile(event.target.value);
  }

  const handleChartChange = (event) => {
    setSelectedChart(event.target.value);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Printed':
        return '#EF6C00';
      case 'Draft':
        return '#00000099';
      default:
        return '#000000DE';
    }
  };

  const renderCell = (row, column) => {
    switch (column.id) {
      case "status":
        return (
          <Button
            size="small"
            sx={{
              width: "60px",
              py: '8px',
              fontSize: '10px',
              borderRadius: '5px',
              textTransform: 'none',
              lineHeight: '100%',
              color: getStatusColor(row.status),
              backgroundColor: `${getStatusColor(row.status)}0D`,
              border: `1px solid ${getStatusColor(row.status)}`,
              "&:hover": {
                backgroundColor: `${getStatusColor(row.status)}1A`,
                border: `1px solid ${getStatusColor(row.status)}`,
              },
            }}
          >
            {row.status}
          </Button >
        );
      case "amount":
        return (
          `$${row.amount.toFixed(2)}`
        );
      default:
        return row[column.id];
    }
  };

  return (
    <Box sx={styles.wrapper}>
      <Box sx={styles.titleContainer}>
        <Typography sx={styles.title}>Dashboard</Typography>
        <Box sx={styles.selectContainer}>
          <Select
            sx={styles.selectProfile}
            value={profile}
            onChange={handleProfileChange}
            displayEmpty
            renderValue={(selected) => {
              if (!selected || selected === '') {
                return (
                  <Typography sx={{
                    color: "text.secondary",
                    fontSize: { sm: '14px', xs: '10px' },
                  }}>
                    Select Profile
                  </Typography>
                );
              }
              return selected;
            }}
            MenuProps={
              {
                PaperProps: {
                  sx: {
                    '& .MuiMenuItem-root': {
                      backgroundColor: '#FFF',
                      pl: 1
                    },
                    '& .MuiMenuItem-root:hover': {
                      backgroundColor: '#FFF'
                    },
                    '& .MuiMenuItem-root:active': {
                      backgroundColor: '#FFF'
                    },
                  },
                },
              }
            }
          >
            <MenuItem value="Select All"><Checkbox checked={false} />Select All</MenuItem>
            <Divider />
            <Typography sx={{
              color: "text.secondary",
              ml: 3,
              mb: 1,
            }}  >Personal Account</Typography>
            <MenuItem value="Abraham Sabel"><Checkbox checked={true} />Abraham Sabel</MenuItem>
            <Divider />
            <Typography sx={{
              color: "text.secondary",
              ml: 3,
              mb: 1,
            }}  >Organizations</Typography>
            <MenuItem value="Synccos"><Checkbox checked={false} />Synccos</MenuItem>
            <MenuItem value="Synccos1"><Checkbox checked={false} />Synccos1</MenuItem>
            <Divider />
            <CustomButton sx={{ ml: 'auto', mr: 1 }}>Apply</CustomButton>
          </Select>
          <Select sx={styles.selectType} value={selectedData} onChange={handleDataChange}>
            <MenuItem value="Total Expenses">View By: <b style={{ color: '#000000DE' }}>Expenses</b></MenuItem>
            <MenuItem value="Total Checks">View By: <b style={{ color: '#000000DE' }}>Checks</b></MenuItem>
          </Select>
        </Box>
      </Box>

      <Box sx={styles.statisticsContainer}>
        <Box sx={styles.cardTotalContainer}>
          <Box sx={styles.cardHeader}>
            <Typography sx={styles.cardTotalTitle}>Total Amount</Typography>
            <CallMadeIcon sx={styles.cardTotalIcon} />
          </Box>
          <Typography sx={styles.cardTotalAmount}>$61.00</Typography>
        </Box>

        <Box sx={styles.cardClearedContainer}>
          <Box sx={styles.cardHeader}>
            <Typography sx={styles.cardClearedTitle}>Cleared</Typography>
            <CallMadeIcon sx={styles.cardClearedIcon} />
          </Box>
          <Typography sx={styles.cardClearedAmount}>$0.00</Typography>
        </Box>

        <Box sx={styles.cardDraftContainer}>
          <Box sx={styles.cardHeader}>
            <Typography sx={styles.cardDraftTitle}>Draft</Typography>
            <CallMadeIcon sx={styles.cardDraftIcon} />
          </Box>
          <Typography sx={styles.cardDraftAmount}>$36.00</Typography>
        </Box>
      </Box>

      <Box sx={styles.chartContainer}>
        <Box sx={styles.chartBox}>
          <Box sx={styles.chartHeader}>
            <Typography sx={styles.chartTitle}>Stats</Typography>
            <Select sx={styles.chartSelect} value={selectedChart} onChange={handleChartChange}>
              <MenuItem value="daily">Daily</MenuItem>
              <MenuItem value="weekly">Weekly</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="yearly">Yearly</MenuItem>
            </Select>
          </Box>
          <Typography sx={styles.chartTitle}>$0.00</Typography>
          <Typography sx={styles.chartDate}>Apr 6, 2025 - Apr 13, 2025</Typography>

          <Box sx={{ width: '100%', height: { sm: 300, xs: 200 }, mt: 1, ml: "-20px" }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={selectedChart === 'daily' ? dailyData : selectedChart === 'weekly' ? weeklyData : selectedChart === 'monthly' ? monthlyData : yearlyData}
                // margin={{ top: 0, right: 10, left: -20, bottom: 0 }}
                margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                <defs>
                  <linearGradient id="coloramt" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#EEFCDB" stopOpacity={1} />
                    <stop offset="95%" stopColor="#EEFCDB" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <XAxis
                  dataKey="name"
                  tick={{ fill: '#666', fontSize: 12 }}
                  axisLine={{ stroke: '#f5f5f5' }}
                  padding={{ left: 10, right: 10 }}
                />
                <YAxis
                  tick={{ fill: '#666', fontSize: 12 }}
                  axisLine={{ stroke: '#f5f5f5' }}
                  padding={{ top: 10, bottom: 10 }}
                />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#C3E5CF"
                  strokeWidth={2}
                  dot={{ fill: '#C3E5CF', r: 3 }}
                  activeDot={{ r: 4 }}
                />
                <Tooltip />

              </LineChart>
            </ResponsiveContainer>

          </Box>
        </Box>
        <Box sx={styles.chartBox}>
          <Box sx={styles.chartHeader}>
            <Typography sx={styles.chartTitle}>Recent Checks</Typography>
            <Button endIcon={<KeyboardArrowRightIcon />} sx={styles.viewAllButton}>
              View All
            </Button>
          </Box>
          <CustomTable
            columns={[
              { id: "no", label: "Check No." },
              { id: "payeeName", label: "Payee Name" },
              { id: "status", label: "Status" },
              { id: "amount", label: "Amount" },
            ]}
            data={recentChecks}
            isCenteredCells={true}
            renderCell={renderCell}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;