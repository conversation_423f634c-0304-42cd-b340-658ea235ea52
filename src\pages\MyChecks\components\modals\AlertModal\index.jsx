import React from "react";
import { <PERSON>, Typo<PERSON>, Button, <PERSON>alog, DialogTitle, DialogContent, DialogActions, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { ClearedCheckIcon, DeleteIcon, VoiceIcon, UnsavedIcon, DownloadCheckIcon } from "../../../../../components/Icons";
import { styles } from "../../../styles";

export const AlertModal = ({ open, onClose, onConfirm, checkData, type }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: styles.dialog
      }}
    >
      <Box sx={styles.dialogHeader}>
        <DialogTitle sx={styles.dialogTitle}>
        </DialogTitle>
        <IconButton
          onClick={onClose}
          sx={styles.dialogCloseButton}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={styles.alertIconContainer}>
          <Box sx={type === "cleared" ? styles.clearedIconBorder : type === 'downloadCheck' ? styles.downloadCheckIconBorder : styles.alertIconBorder}>
            <Box sx={type === "cleared" ? styles.clearedIconMain : type === 'downloadCheck' ? styles.downloadCheckIconMain : styles.alertIconMain}>
              {type === 'delete' || type === "delete_attachment" ? <DeleteIcon /> : type === "void" ? <VoiceIcon width="56px" height="56px" color="#FFF" /> : type === "cleared" ? <ClearedCheckIcon /> : type === 'downloadCheck' ? <DownloadCheckIcon /> : <UnsavedIcon color = "white" />}
            </Box>
          </Box>
        </Box>
        <Typography sx={{ ...styles.dialogTitle, xs: { mb: '19px' }, mb: '30px' }} align="center"  >
          {type === 'delete' ? 'Confirm Deletion?' : type === "void" ? 'Void This Check?' : type === "cleared" ? 'Change to Cleared?' : type === 'unsaved' ? 'Unsaved Changes' : type === 'delete_attachment' ? 'Delete Attachment?' :  'Check Generated Successfully'}
        </Typography>
        <Typography sx={styles.dialogContent} color="text.secondary" align="center"  >
          {type === 'delete' ? `Are you sure you want to delete this check no. ${checkData && checkData.no} which is Draft?` : type === "void" ? `Are you sure you want to void this check? This action cannot be undone and will mark the check as invalid.` : type === "cleared" ? `The check status is currently Submitted. Are you sure you want to update it to Cleared?` : type === 'unsaved' ? `You have unsaved changes. If you exit now, any entered data will be lost.` : type === 'delete_attachment' ? 'Are you sure you want to delete this file? This action cannot be undone.' : `Your check is successfully generated. Now you can view or download your check.`}
        </Typography>

      </DialogContent>
      <DialogActions sx={styles.deleteDialogActions}>
        {type !== 'downloadCheck' && <Button
          onClick={onClose}
          variant="outlined"
          sx={styles.cancelButton}
        >
          Cancel
        </Button>}
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={styles.confirmButton}
        >
          {type === 'delete' || type == "delete_attachment" ? 'Delete' : type === "void" ? 'Void Check' : type === "cleared" ? 'Confirm Change' : type === 'unsaved' ? 'Discard Changes' : 'Download Check'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
