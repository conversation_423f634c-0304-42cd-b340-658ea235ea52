import { useState } from "react";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { Box, Fade, MenuItem, Paper, Popper, Typography } from "@mui/material";
import { getStatusColor } from "../../../index";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { styles } from "../../../styles";
import { ClearedIcon, PrintIcon, MailboxIcon, EmailIcon, CheckDeleteIcon, AttachmentPDFIcon } from "../../../../../components/Icons";
import { AttachmentItem } from '../../AttachmentItem';

const otherTags = [
  { color: '#058205', value: 'Business' },
  { color: '#EF6C00', value: 'Personal' },
  { color: '#D2BA00', value: 'Miscellaneous' },
]
const myTags = [
  { color: '#F03D3E', value: 'Tag1' },
  { color: '#3EA5F9', value: 'Tag2' },
  { color: '#00CF72', value: 'Tag3' },
]

export const DetailModal = ({ open, onClose, checkData, handlePrint, handleDelete, handleCleared, handleEmail }) => {

  const [tagAnchorEl, setTagAnchorEl] = useState(null);
  const [attachmentsAnchorEL, setAttachmentsAnchorEl] = useState(null);

  const handleMail = () => {
    console.log("mail");
  }

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      width="607px"
      title="Checks Details"
      content={
        <Box>
          <Typography color="#00000099" sx={{ mb: "24px" }}>
            View generated check details
          </Typography>

          <Box sx={styles.detailContainer}>
            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Check No.
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.checkNumber}
              </Typography>
            </Box>
            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Amount
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.amount}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Bank Account
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.bankAccount}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Payee Name
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.payeeName}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Issued Date
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.issuedDate}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Status
              </Typography>
              <Typography sx={styles.detailValue}>
                <CustomButton
                  size="small"
                  sx={{
                    width: '110px',
                    color: getStatusColor(checkData && checkData.status),
                    backgroundColor: `${getStatusColor(checkData && checkData.status)}0D`,
                    border: `1px solid ${getStatusColor(checkData && checkData.status)}`,
                    '&:hover': {
                      backgroundColor: `${getStatusColor(checkData && checkData.status)}1A`,
                      border: `1px solid ${getStatusColor(checkData && checkData.status)}`
                    }
                  }}
                >
                  {checkData && checkData.status}
                </CustomButton>
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Invoice ID
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.invoiceId}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Tags
              </Typography>
              <Box sx={{ ...styles.detailValue, display: 'flex', gap: '12px', alignItems: 'center' }}>
                {(checkData?.tags || []).slice(0, 3).map(((tag, index) => (
                  <Box key={"tag_detail" + index} sx={styles.selectTagItem}>
                    <Box sx={{ ...styles.tag, backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color }} />
                    {tag}
                  </Box>
                )))}
                {(checkData?.tags || []).length > 3 && <Box>
                  <Typography
                    sx={{ color: "#000000DE", cursor: 'pointer' }}
                    onMouseEnter={(event) => {
                      setTagAnchorEl(event.currentTarget)
                    }}
                    onMouseLeave={() => {
                      setTagAnchorEl(null)
                    }}
                  >
                    +{(checkData?.tags || []).length - 3}
                  </Typography>

                  <Popper
                    open={Boolean(tagAnchorEl)}
                    anchorEl={tagAnchorEl}
                    placement="bottom"
                    style={{ zIndex: 9999 }}
                    transition
                    modifiers={[
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 8],
                        },
                      },
                    ]}
                  >
                    {({ TransitionProps }) => (
                      <Fade {...TransitionProps} timeout={200}>
                        <Paper sx={styles.tagTablePaper}>
                          {(checkData?.tags.slice(3) || [])?.map((tag, index) => (
                            <MenuItem
                              key={index}
                              sx={styles.tagTableItem}
                            >
                              <Box
                                sx={{
                                  ...styles.tag,
                                  backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color
                                }}
                              />
                              {tag}
                            </MenuItem>
                          ))}
                        </Paper>
                      </Fade>
                    )}
                  </Popper>

                </Box>}

              </Box>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Account Nickname
              </Typography>
              <Typography sx={styles.detailValue}>
                {checkData && checkData.accountNickname}
              </Typography>
            </Box>

            <Box sx={styles.detailItem}>
              <Typography sx={styles.detailTitle}>
                Attachments
              </Typography>
              <Box sx={{ ...styles.detailValue, display: 'flex', gap: '12px', alignItems: 'center' }}>
                {(checkData?.attachments || []).slice(0, 2).map(((attachment, index) => (
                  <AttachmentItem
                    key={'attachment_detail_show' + index}
                    attachment={attachment}
                  />
                )))}
                {(checkData?.attachments || []).length > 2 && <Box>
                  <Typography
                    sx={{ color: "#000000DE", cursor: 'pointer' }}
                    onMouseEnter={(event) => {
                      setAttachmentsAnchorEl(event.currentTarget)
                    }}
                    onMouseLeave={() => {
                      setAttachmentsAnchorEl(null)
                    }}
                  >
                    +{(checkData?.attachments || []).length - 2}
                  </Typography>

                  <Popper
                    open={Boolean(attachmentsAnchorEL)}
                    anchorEl={attachmentsAnchorEL}
                    placement="bottom"
                    style={{ zIndex: 9999 }}
                    transition
                    modifiers={[
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 8],
                        },
                      },
                    ]}
                  >
                    {({ TransitionProps }) => (
                      <Fade {...TransitionProps} timeout={200}>
                        <Paper sx={{ ...styles.tagTablePaper, minWidth: '230px' }}>
                          {(checkData?.attachments.slice(2) || [])?.map((attachment, index) => (
                            <AttachmentItem
                              key={'attachment_detail' + index}
                              attachment={attachment}
                            />
                          ))}
                        </Paper>
                      </Fade>
                    )}
                  </Popper>
                </Box>}
              </Box>
            </Box>

          </Box>
        </Box>
      }
      actions={
        <Box sx={styles.actionsContainer}>
          <CustomButton
            startIcon={<CheckDeleteIcon color="#FF4D4F" />}
            variant="outlined"
            sx={styles.actionDeleteButton}
            onClick={handleDelete}
          >
            Delete
          </CustomButton>
          <CustomButton
            startIcon={<MailboxIcon width="16px" height="16px" />}
            variant="outlined"
            sx={styles.actionButton}
            onClick={handleMail}
          >
            Mail
          </CustomButton>
          <CustomButton
            startIcon={<EmailIcon color="#000000DE" />}
            variant="outlined"
            sx={styles.actionButton}
            onClick={handleEmail}
          >
            Email
          </CustomButton>

          <CustomButton
            startIcon={<PrintIcon color="#000000DE" />}
            variant="outlined"
            sx={styles.actionButton}
            onClick={handlePrint}
          >
            Print
          </CustomButton>
          <CustomButton
            startIcon={<ClearedIcon color="#FFF" />}
            variant="outlined"
            color="primary"
            onClick={handleCleared}
          >
            Cleared Check
          </CustomButton>
        </Box>
      }
    />
  );
};
