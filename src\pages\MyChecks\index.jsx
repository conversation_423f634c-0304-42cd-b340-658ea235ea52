import {
  Box,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  Pagination,
  PaginationItem,
  Checkbox,
  Menu,
  MenuItem,
  IconButton,
  Button,
  Select,
  Tooltip,
  Divider,
  Popper,
  Paper,
  Fade
} from '@mui/material';
import { useState, useEffect } from 'react';
import { CustomTable } from '../../components/table/CustomTable';
import { CustomButton } from '../../components/buttons/CustomButton';
//Mui-icons
import AddIcon from '@mui/icons-material/Add';
import { FilterIcon, MailboxIcon, MoreVerticalIcon, SearchIcon, EditIcon, EEmailIcon, CheckDeleteIcon, PrintIcon, VoiceIcon, DetailIcon, ClearedIcon, AttachmentPDFIcon } from '../../components/Icons';
import CloseIcon from '@mui/icons-material/Close';
import { styles } from './styles';
import { EmailCheckModal, SendMailModal, RestrictModal, DetailModal, AlertModal, AddModal, GeneratingCheckModal, BlankCheckModal, EditModal, AttachmentsModal } from './components/modals';
import FilterMenu from './components/FilterMenu';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import LinkIcon from '@mui/icons-material/Link';
import { AttachmentItem } from './components/AttachmentItem';

const otherTags = [
  { color: '#058205', value: 'Business' },
  { color: '#EF6C00', value: 'Personal' },
  { color: '#D2BA00', value: 'Miscellaneous' },
]
const myTags = [
  { color: '#F03D3E', value: 'Tag1' },
  { color: '#3EA5F9', value: 'Tag2' },
  { color: '#00CF72', value: 'Tag3' },
]

const mockChecks = [
  {
    id: 1,
    checkNumber: 1,
    payeeName: 'Abraham Sabel',
    amount: 123412.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Blank',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous', 'Tag1', 'Tag2'],
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
      { id: 3, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
    invoiceId: '12345'
  },
  {
    id: 2,
    checkNumber: 2,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Submitted',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous'],
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
    invoiceId: '12345'
  },
  {
    id: 3,
    checkNumber: 3,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Printed',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal'],
    attachments: [],
    invoiceId: '12345'
  },
  {
    id: 4,
    checkNumber: 4,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Mailed',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous', 'Tag1'],
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
    invoiceId: '12345'
  },
  {
    id: 5,
    checkNumber: 5,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Emailed',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal',],
    invoiceId: '12345'
  },
  {
    id: 6,
    checkNumber: 6,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Cleared',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous', 'Tag1', 'Tag2'],
    invoiceId: '12345'
  },
  {
    id: 7,
    checkNumber: 7,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Voided',
    bankAccount: 'Bank of America',
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous', 'Tag1', 'Tag2'],
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
    invoiceId: '12345'
  },
  {
    id: 8,
    checkNumber: 8,
    payeeName: 'Abraham Sabel',
    amount: 1.59,
    issuedDate: '2/12/2025',
    accountNickname: 'Testing101',
    Tags: '',
    status: 'Draft',
    bankAccount: 'Bank of America',
    type: "blanked",
    signature: true,
    tags: ['Business', 'Personal', 'Miscellaneous', 'Tag1', 'Tag2'],
    invoiceId: '12345'
  }
];

export const getStatusColor = (status) => {
  switch (status) {
    case 'Printed':
      return '#EF6C00';
    case 'Submitted':
      return '#204464';
    case 'Mailed':
      return '#CBB300';
    case 'Emailed':
      return '#3EA5F9';
    case 'Cleared':
      return '#058205'
    case 'Voided':
      return '#F03D3E';
    case 'Blank':
      return '#CECECE';
    case 'Draft':
      return "#********";
    default:
      return '#000000';
  }
};

function isEmpty(value) {
  return (
    value === undefined ||
    value === null ||
    (typeof value === 'string' && value.trim() === '') ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
  );
}

function isMultiEmpty(values) {
  let flag = 0;
  Object.keys(values).forEach(key => {
    if (!isEmpty(values[key]))
      flag = 1
  })
  if (flag === 1)
    return false;
  return true;
}

const MyChecks = () => {
  const [checks, setChecks] = useState(mockChecks);
  const [selectedChecks, setSelectedChecks] = useState([]);
  const [isCheckedAll, setIsCheckedAll] = useState(false);
  const [openMailDialog, setOpenMailDialog] = useState(false);
  const [openRestrictDialog, setOpenRestrictDialog] = useState(false);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openAlertDialog, setOpenAlertDialog] = useState(false);
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState(false);
  const [alertType, setAlertType] = useState(null);
  const [page, setPage] = useState(1);

  const [anchorEl, setAnchorEl] = useState(null);
  const [tagAnchorEl, setTagAnchorEl] = useState(null);
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openGeneratingCheckDialog, setOpenGeneratingCheckDialog] = useState(false);
  const [disableEmailButton, setDisableEnableButton] = useState(true);
  const [openEmailCheckDialog, setOpenEmailCheckDialog] = useState(false);
  const [openBlankCheckDialog, setOpenBlankCheckDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedAttachments, setSelectedAttachments] = useState([]);
  const [attachmentAnchorEL, setAttachmentAnchorEL] = useState(null);

  const [filterList, setFilterList] = useState({
    singleFilter: ['All Checks', 'Cleared Checks', 'Draft Checks', 'Cleared Checks'],
    multiFilter: { "Payee Name": ["Abraham Sabel"], "Account Nickname": ["Hello"] },
  });

  useEffect(() => {
    if (checks.length > 0) {
      setIsCheckedAll(selectedChecks.length === checks.length);
    }
  }, [selectedChecks, checks.length]);

  useEffect(() => {
    console.log("FilterList ===================>", filterList)
  }, [filterList])

  const handleMenuOpen = (event, data) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(checks.find(c => c.id === data.id));
  };



  const handleSelectAll = (event) => {
    const checked = event.target.checked;
    setIsCheckedAll(checked);

    if (checked) {
      setSelectedChecks(checks);
    } else {
      setSelectedChecks([]);
    }
  };

  const handleSelectCheck = (checkId) => {
    setSelectedChecks(prev => {
      const check = checks.find(c => c.id === checkId);
      const isSelected = prev.some(selectedCheck => selectedCheck.id === checkId);

      if (isSelected) {
        return prev.filter(selectedCheck => selectedCheck.id !== checkId);
      } else {
        return [...prev, check];
      }
    });
  };

  const handleMailClick = () => {
    setOpenMailDialog(true);
    // setOpenRestrictDialog(true);
  }

  const handleUpgradeNow = () => {
    setOpenRestrictDialog(false);
  }

  const handleConfirmMail = (checks) => {
    console.log('final checks for mail ==>', checks);
  }

  const renderHeaderCell = (column) => {
    switch (column.id) {
      case 'selected':
        return (
          <Checkbox
            checked={isCheckedAll}
            onChange={handleSelectAll}
            sx={styles.checkbox}
          />
        );
      default:
        return column.label;
    }
  }

  const renderCell = (row, column) => {
    switch (column.id) {
      case 'selected':
        return (
          <Checkbox
            checked={selectedChecks.some(check => check.id === row.id)}
            onChange={() => handleSelectCheck(row.id)}
            sx={styles.checkbox}
          />
        )
      case 'status':
        return (
          <>
            {row.status === "Blank" || row.status === "Cleared" || row.status === "Voided" || row.status === "Draft" ? <CustomButton
              size="small"
              sx={{
                width: '110px',
                color: getStatusColor(row.status),
                backgroundColor: `${getStatusColor(row.status)}0D`,
                border: `1px solid ${getStatusColor(row.status)}`,
                '&:hover': {
                  backgroundColor: `${getStatusColor(row.status)}1A`,
                  border: `1px solid ${getStatusColor(row.status)}`
                },
                '& .MuiBox-root': {
                  display: 'flex',
                  alignItems: 'center',
                  gap: '5px'
                }
              }}
            >
              {row.status}
              {row.type === "blanked" && <img src="img/blankedCheck_draft.png" width="20px" height="16px" alt="blanked" />}
            </CustomButton> :
              <Select
                fullWidth
                value={row.status}
                onChange={(e) => {
                  if (e.target.value === "Cleared") {
                    setOpenAlertDialog(true);
                    setAlertType("cleared");
                    setSelectedItem(row);
                  }
                }}
                sx={{
                  width: '110px',
                  height: '30px',
                  fontSize: '12px',
                  color: getStatusColor(row.status),
                  backgroundColor: `${getStatusColor(row.status)}0D`,
                  border: `1px solid ${getStatusColor(row.status)}`,
                  borderRadius: '5px',
                  '&:hover': {
                    backgroundColor: `${getStatusColor(row.status)}1A`,
                    border: `1px solid ${getStatusColor(row.status)}`
                  },

                  "& .MuiSvgIcon-root": {
                    color: getStatusColor(row.status),
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      mt: '4px',
                      borderRadius: '5px',
                      '& .MuiMenuItem-root': {
                        height: '30px',
                        fontSize: '12px',
                      },
                      '& .MuiMenuItem-root:first-of-type': {
                        display: 'none'
                      },
                      '& .MuiList-root': {
                        padding: '0px',
                      }
                    }
                  }
                }}
              >
                <MenuItem value={row.status}>{row.status}</MenuItem>
                <MenuItem value={"Cleared"}>Cleared</MenuItem>
              </Select>
            }
          </>
        );
      case "tags":
        return (
          <Box
            sx={{ position: 'relative', display: 'flex', justifyContent: 'center' }}
          >
            <Button
              onMouseEnter={(event) => {
                setTagAnchorEl(event.currentTarget)
                setSelectedTags(row.tags)
              }}
              onMouseLeave={() => {
                setTagAnchorEl(null)
              }}
              sx={styles.tagButton}
            >
              <Box sx={{ display: 'flex' }}>
                {
                  row.tags.slice(0, 3).map((tag, index) => <Box key={"tag_input_table" + index} sx={{ ...styles.tag, backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color, ml: '-2px' }} />)
                }
              </Box>
              {row.tags?.length || 0} Tags
            </Button>
          </Box>
        );
      case "attachment":
        return row.attachments?.length > 0 && (
          <AttachFileIcon
            onMouseEnter={(event) => {
              setAttachmentAnchorEL(event.currentTarget)
              setSelectedAttachments(row.attachments)
            }}
            onMouseLeave={() => {
              setAttachmentAnchorEL(null)
            }}
            sx={{ width: '16px', height: '16px', rotate: '45deg', color: '#********' }}
          />
        )
      case "actions":
        return (
          <IconButton
            sx={styles.moreButton}
            onClick={(e) => handleMenuOpen(e, row)}
          >
            <MoreVerticalIcon />
          </IconButton>
        );
      case "amount":
        return `$${row.amount}`
      default:
        return row[column.id];
    }
  };

  const handleDelete = () => {
    console.log("delete", selectedChecks, selectedItem);
    setOpenAlertDialog(false);
  }

  const handlePrint = () => {
    console.log('print', selectedItem);
    setAnchorEl(null);
    setOpenGeneratingCheckDialog(true);
    setTimeout(() => {
      setOpenGeneratingCheckDialog(false);
      setAlertType("downloadCheck");
      setOpenAlertDialog(true);
    }, 2000);
  }

  const handleVoid = () => {
    console.log('void', selectedItem);
    setAnchorEl(null);

  }

  const handleCleared = () => {
    console.log('cleared', selectedItem);
    setAnchorEl(null);

  }

  const handleDetail = () => {
    setOpenDetailDialog(true);
    setAnchorEl(null);
  }

  const handleAttachments = () => {
    setAnchorEl(null);
    setOpenAttachmentsModal(true);
  }

  const handleOpenUnsavedAlert = () => {
    setOpenAlertDialog(true);
    setAlertType("unsaved");
  }

  const handleOpenDownloadCheckAlert = () => {
    setOpenGeneratingCheckDialog(false);
    setOpenAlertDialog(true);
    setAlertType("downloadCheck");
  }

  const handleUnsaved = () => {
    setOpenAddDialog(false);
    setOpenBlankCheckDialog(false);
    setOpenEditDialog(false);
    setOpenAlertDialog(false);
  }

  const handleDownloadCheck = () => {
    console.log('download check', selectedItem);
  }

  const handleSingleFilterClose = (lable) => {
    setFilterList({ ...filterList, singleFilter: filterList.singleFilter.filter(val => val !== lable) })
  }

  const handleMultiFilterClose = (key, lable) => {
    setFilterList({
      ...filterList,
      multiFilter: { ...filterList.multiFilter, [key]: filterList.multiFilter[key].filter(val => val !== lable) }
    })
  }

  const handleResetFilter = () => {
    setFilterList({
      singleFilter: [],
      multiFilter: {},
    })
  }

  const handleAttachmentSave = (files) => {
    console.log("files", files)
  }

  return (
    <Box sx={styles.wrapper}>
      <Typography sx={styles.title}>
        My Checks
      </Typography>

      <Typography
        variant="body1"
        color="text.secondary"
        sx={styles.description}
      >
        Below is the list of all your checks.
      </Typography>

      <Box sx={styles.actionContainer}>
        <Box sx={styles.searchContainer}>
          <CustomButton
            variant="outlined"
            endIcon={<FilterIcon color="#1a3850" />}
            color="secondary"
            onClick={(e) => setFilterMenuAnchorEl(e.currentTarget)}
          >
            Filter
          </CustomButton>
          <TextField
            placeholder="Search"
            sx={{ ...styles.searchField }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" sx={styles.searchIconContainer}>
                  <SearchIcon sx={styles.searchIcon} />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        <Box sx={styles.actionButtons}>
          {selectedChecks.length > 0 && <><CustomButton
            variant="outlined"
            startIcon={<CheckDeleteIcon color="#FF4D4F" />}
            disabled={selectedChecks.length === 0}
            color="error"
            onClick={() => {
              setOpenAlertDialog(true);
              setAlertType("delete");
            }}
          >
            Delete
          </CustomButton>
            <CustomButton
              variant="outlined"
              startIcon={<PrintIcon color="#000000DE" />}
              disabled={selectedChecks.length === 0}
              sx={styles.actionButton}
              onClick={handlePrint}
            >
              Print
            </CustomButton>
            {!disableEmailButton ? (
              <Tooltip
                title="Emails cannot be sent with multiple payees. Please ensure all checks have the same payee to proceed."
                placement="bottom"
                arrow
                componentsProps={{
                  tooltip: { sx: { ...styles.tooltip } },
                }}
              >
                <span style={{ cursor: "pointer" }}>
                  <Button
                    variant="outlined"
                    startIcon={<EEmailIcon color="#CECECE" />}
                    disabled={true}
                    sx={{ ...styles.actionButton, borderRadius: "8px", height: "40px" }}
                  >
                    Email
                  </Button>
                </span>
              </Tooltip>
            ) : (
              <CustomButton
                variant="outlined"
                startIcon={<EEmailIcon color="#000000DE" />}
                disabled={false}
                sx={{ ...styles.actionButton, borderRadius: "8px" }}
                onClick={() => setOpenEmailCheckDialog(true)}
              >
                Email
              </CustomButton>
            )}
          </>}
          <CustomButton
            variant="outlined"
            startIcon={<MailboxIcon />}
            disabled={selectedChecks.length === 0}
            sx={styles.actionButton}
            onClick={handleMailClick}
          >
            Mail
          </CustomButton>

          <CustomButton
            variant="outlined"
            startIcon={<AddIcon />}
            color="secondary"
            onClick={() => {
              setOpenBlankCheckDialog(true);
              setSelectedItem(null);
            }}
          >
            Blank
          </CustomButton>

          <CustomButton
            variant="outlined"
            startIcon={<AddIcon />}
            color="primary"
            onClick={() => {
              setOpenAddDialog(true);
              setSelectedItem(null);
            }}
          >
            New
          </CustomButton>
        </Box>
      </Box>

      {(!isEmpty(filterList.singleFilter) || (!isEmpty(filterList.multiFilter) && !isMultiEmpty(filterList.multiFilter))) && <><Divider />

        <Box sx={styles.filterContainer}>
          {
            filterList.singleFilter.map((val, index) => (
              <Box key={`Filter_Check_${index}`} sx={styles.filterItemContainer}>
                <Typography sx={styles.filterlableTitle}>{val}</Typography>
                <CloseIcon onClick={() => handleSingleFilterClose(val)} sx={styles.filterCloseIcon} />
              </Box>
            ))
          }
          {
            Object.keys(filterList.multiFilter).map((keyText, index) => {
              return filterList.multiFilter[keyText].map((val, subIndex) => <Box key={`Filter_Check_${index}_${subIndex}`} sx={styles.filterItemContainer}>
                <Typography sx={styles.filterlableTitle}>{keyText}: <Box component="span" sx={{ color: '#CECECE', ml: '4px', fontSize: '12px' }}>{val}</Box></Typography>
                <CloseIcon onClick={() => handleMultiFilterClose(keyText, val)} sx={styles.filterCloseIcon} />
              </Box>)
            })
          }
          <Button
            variant="outlined"
            sx={styles.resetButton}
            onClick={handleResetFilter}
          >Reset</Button>
        </Box></>}

      <CustomTable
        columns={[
          { id: 'selected', label: '', width: '40px' },
          { id: 'checkNumber', label: 'Check No.', width: '70px' },
          { id: 'status', label: 'Status', width: '120px' },
          { id: 'amount', label: 'Amount', width: '100px' },
          { id: 'payeeName', label: 'Payee Name', width: '160px' },
          { id: 'issuedDate', label: 'Issued Date', width: '140px' },
          { id: 'accountNickname', label: 'Account Nickname', width: '140px' },
          { id: 'tags', label: 'Tags', width: '120px' },
          { id: 'attachment', label: '', },
          { id: 'actions', label: 'Actions', width: '80px' },
        ]}
        data={checks}
        renderCell={renderCell}
        renderHeaderCell={renderHeaderCell}
        isCenteredCells={true}
      />

      <Box sx={styles.paginationContainer}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                previous: () => 'Previous',
                next: () => 'Next',
              }}
              {...item}
              sx={styles.paginationItem}
            />
          )}
          sx={styles.pagination}
        />
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        PaperProps={{
          sx: styles.menuPaper,
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={() => { setOpenEditDialog(true); setAnchorEl(null); }} sx={styles.menuItem}>
          <EditIcon />
          Edit
        </MenuItem>
        <MenuItem onClick={() => { setOpenEmailCheckDialog(true); setAnchorEl(null); setSelectedChecks([selectedItem]) }} sx={styles.menuItem}>
          <EEmailIcon />
          Email
        </MenuItem>
        <MenuItem onClick={() => { setOpenAlertDialog(true); setAnchorEl(null); setAlertType('delete') }} sx={styles.menuItem}>
          <CheckDeleteIcon />
          Delete
        </MenuItem>
        <MenuItem onClick={handlePrint} sx={styles.menuItem}>
          <PrintIcon />
          Print
        </MenuItem>
        <MenuItem onClick={() => { setOpenAlertDialog(true); setAnchorEl(null); setAlertType('void') }} sx={styles.menuItem}>
          <VoiceIcon />
          Void Check
        </MenuItem>
        <MenuItem onClick={() => { setOpenAlertDialog(true); setAnchorEl(null); setAlertType('cleared') }} sx={styles.menuItem}>
          <ClearedIcon />
          Cleared Check
        </MenuItem>
        <MenuItem onClick={handleDetail} sx={styles.menuItem}>
          <DetailIcon />
          View Details
        </MenuItem>
        <MenuItem onClick={handleAttachments} sx={styles.menuItem}>
          <LinkIcon sx={{rotate: '-45deg', width: '18px', height: '18px'}} />
          Attachments
        </MenuItem>
      </Menu>

      <FilterMenu
        anchorEl={filterMenuAnchorEl}
        setAnchorEl={setFilterMenuAnchorEl}
        setFilterList={setFilterList}
        filterList={filterList}
      />

      <Popper
        open={Boolean(tagAnchorEl)}
        anchorEl={tagAnchorEl}
        placement="bottom"
        style={{ zIndex: 9999 }}
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 8],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={200}>
            <Paper sx={styles.tagTablePaper}>
              {selectedTags?.map((tag, index) => (
                <MenuItem
                  key={index}
                  sx={styles.tagTableItem}
                >
                  <Box
                    sx={{
                      ...styles.tag,
                      backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color
                    }}
                  />
                  {tag}
                </MenuItem>
              ))}
            </Paper>
          </Fade>
        )}
      </Popper>

      <Popper
        open={Boolean(attachmentAnchorEL)}
        anchorEl={attachmentAnchorEL}
        placement="bottom-end"
        style={{ zIndex: 9999 }}
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 8],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={200}>
            <Paper sx={{...styles.tagTablePaper, minWidth: '230px'}}>
              {selectedAttachments?.map((attachment, index) => (
                <AttachmentItem attachment={attachment} key = {"table_attachment" + index} />
              ))}
            </Paper>
          </Fade>
        )}
      </Popper>

      <SendMailModal
        open={openMailDialog}
        onClose={() => setOpenMailDialog(false)}
        checks={selectedChecks}
        onConfirm={handleConfirmMail}
      />

      <RestrictModal
        open={openRestrictDialog}
        onClose={() => setOpenRestrictDialog(false)}
        onConfirm={handleUpgradeNow}
      />

      <DetailModal
        open={openDetailDialog}
        onClose={() => setOpenDetailDialog(false)}
        checkData={selectedItem}
        handlePrint={() => {
          setOpenDetailDialog(false);
          handlePrint()
        }}
        handleDelete={() => {
          setOpenDetailDialog(false);
          setOpenAlertDialog(true); setAlertType('delete')
        }}
        handleCleared={() => {
          setOpenDetailDialog(false);
          setOpenAlertDialog(true); setAlertType('cleared')
        }}
        handleEmail={() => {
          setOpenDetailDialog(false);
          setOpenEmailCheckDialog(true);
          setSelectedChecks([selectedItem])
        }}
      />

      <AlertModal
        open={openAlertDialog}
        onClose={() => setOpenAlertDialog(false)}
        checkData={selectedItem}
        onConfirm={alertType === 'delete' ? handleDelete : alertType === 'void' ? handleVoid : alertType === 'cleared' ? handleCleared : alertType === 'unsaved' ? handleUnsaved : handleDownloadCheck}
        type={alertType}
      />

      <AddModal
        open={openAddDialog}
        onClose={() => setOpenAddDialog(false)}
        checkData={selectedItem}
        handleOpenUnsavedAlert={handleOpenUnsavedAlert}
        handleOpenDownloadCheckAlert={handleOpenDownloadCheckAlert}
        setOpenGeneratingCheckDialog={setOpenGeneratingCheckDialog}
      />

      <EditModal
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        checkData={selectedItem}
        handleOpenUnsavedAlert={handleOpenUnsavedAlert}
        handleOpenDownloadCheckAlert={handleOpenDownloadCheckAlert}
        setOpenGeneratingCheckDialog={setOpenGeneratingCheckDialog}
      />


      <BlankCheckModal
        open={openBlankCheckDialog}
        onClose={() => setOpenBlankCheckDialog(false)}
        checkData={selectedItem}
        handleOpenUnsavedAlert={handleOpenUnsavedAlert}
        handleOpenDownloadCheckAlert={handleOpenDownloadCheckAlert}
        setOpenGeneratingCheckDialog={setOpenGeneratingCheckDialog}
      />

      <GeneratingCheckModal
        open={openGeneratingCheckDialog}
      />

      <EmailCheckModal
        open={openEmailCheckDialog}
        onClose={() => setOpenEmailCheckDialog(false)}
        selectedChecks={selectedChecks}
        onConfirm={() => { }}
      />

      <AttachmentsModal 
        open={openAttachmentsModal}
        onClose={() => setOpenAttachmentsModal(false)}
        title = "View Attachments"
        handleSave={handleAttachmentSave}
        attachments={selectedItem?.attachments}
      />
    </Box>
  );
};

export default MyChecks;
