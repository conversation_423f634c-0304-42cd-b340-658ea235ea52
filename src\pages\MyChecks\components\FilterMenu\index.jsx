import React, { useState, useEffect } from 'react';
import {
  Box,
  Menu,
  MenuItem,
  ListItemText,
  Popper,
  Paper,
  ClickAwayListener,
  TextField,
  InputAdornment,
  Checkbox,
  Typography,
  Divider,
} from '@mui/material';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { styles } from './styles';
import { SearchIcon } from '../../../../components/Icons';
import { CustomButton } from '../../../../components/buttons/CustomButton';

const FilterMenu = ({ anchorEl, setAnchorEl, setFilterList, filterList }) => {
  const [subAnchorEl, setSubAnchorEl] = useState(null);
  const [hoveredItem, setHoveredItem] = useState(null);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [tags, setTags] = useState([
    'Tag 1',
    'Tag 2',
    'Tag 3',
    'Tag 4',
  ]);
  const [accountNicknames, setAccountNicknames] = useState([
    'Account 1',
    'Account 2',
    'Account 3',
    'Account 4',
  ]);

  const [statuses, setStatuses] = useState([
    'Pending',
    'Cleared',
    'Uncleared',
    'Draft',
    'Unknown',
  ]);

  const [bankAccounts, setBankAccounts] = useState([
    'Chase Bank',
    'Bank of America',
    'Wells Fargo',
    'Citibank',
  ]);
  const [payees, setPayees] = useState([
    'John Doe',
    'Jane Smith',
    'Acme Corp',
    'Abraham Sabel',
  ]);

  const [dateRanges, setDateRanges] = useState({
    thisWeek: { start: '', end: '' },
    thisMonth: { start: '', end: '' },
    thisQuarter: { start: '', end: '' },
    thisYear: { start: '', end: '' },
    lastWeek: { start: '', end: '' },
    lastMonth: { start: '', end: '' },
    lastQuarter: { start: '', end: '' },
    lastYear: { start: '', end: '' },
  });

  useEffect(() => {
    const today = new Date();

    // This Week
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay());
    const thisWeekEnd = new Date(thisWeekStart);
    thisWeekEnd.setDate(thisWeekStart.getDate() + 6);

    // This Month
    const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // This Quarter
    const currentQuarter = Math.floor(today.getMonth() / 3);
    const thisQuarterStart = new Date(today.getFullYear(), currentQuarter * 3, 1);
    const thisQuarterEnd = new Date(today.getFullYear(), (currentQuarter + 1) * 3, 0);

    // This Year
    const thisYearStart = new Date(today.getFullYear(), 0, 1);
    const thisYearEnd = new Date(today.getFullYear(), 11, 31);

    // Last Week
    const lastWeekStart = new Date(thisWeekStart);
    lastWeekStart.setDate(thisWeekStart.getDate() - 7);
    const lastWeekEnd = new Date(lastWeekStart);
    lastWeekEnd.setDate(lastWeekStart.getDate() + 6);

    // Last Month
    const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);

    // Last Quarter
    const lastQuarterStart = new Date(today.getFullYear(), (currentQuarter - 1) * 3, 1);
    const lastQuarterEnd = new Date(today.getFullYear(), currentQuarter * 3, 0);

    // Last Year
    const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
    const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31);

    setDateRanges({
      thisWeek: {
        start: thisWeekStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: thisWeekEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      thisMonth: {
        start: thisMonthStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: thisMonthEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      thisQuarter: {
        start: thisQuarterStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: thisQuarterEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      thisYear: {
        start: thisYearStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: thisYearEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      lastWeek: {
        start: lastWeekStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: lastWeekEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      lastMonth: {
        start: lastMonthStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: lastMonthEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      lastQuarter: {
        start: lastQuarterStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: lastQuarterEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
      lastYear: {
        start: lastYearStart.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        end: lastYearEnd.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' })
      },
    });
  }, []);

  const handleClose = () => {
    setAnchorEl(null);
    setHoveredItem(null);
  };

  const handleHover = (item, e) => {
    setHoveredItem(item);
    setSubAnchorEl(e.currentTarget);
  };
  const handleLeave = () => {
    setHoveredItem(null);
    setSubAnchorEl(null);
  };
  const handleMenuClick = (item) => {
    if (!item.subMenu) {
      setFilterList((prevFilter) => {
        return {
          ...prevFilter,
          singleFilter: prevFilter.singleFilter.includes(item.label) ? prevFilter.singleFilter : [...prevFilter.singleFilter, item.label]
        }
      })
      setAnchorEl(null);
      setHoveredItem(null);
    }
  };

  const handleSubMenu = (key, val) => {
    setFilterList((prevFilter) => {
      return {
        ...prevFilter,
        multiFilter: {
          ...prevFilter.multiFilter,
          [key]: (prevFilter.multiFilter[key] || []).includes(val) ? (prevFilter.multiFilter[key] || []).filter(item => item !== val) : [...(prevFilter.multiFilter[key] || []), val]
        }
      }
    })
  }
  const handleDateRange = (val) => {
    setFilterList((prevFilter) => {
      return {
        ...prevFilter,
        multiFilter: {
          ...prevFilter.multiFilter,
          "Issued Date": [val]
        }
      }
    })
    setHoveredItem(null);
    setAnchorEl(null);
  }

  const menuItems = [
    { label: 'All Checks', subMenu: false },
    { label: 'Cleared Checks', subMenu: false },
    { label: 'Uncleared Checks', subMenu: false },
    { label: 'Draft Checks', subMenu: false },
    { label: 'Unknown', subMenu: false },
    { label: 'Bank account', subMenu: true },
    { label: 'Payee Name', subMenu: true },
    { label: 'Tags', subMenu: true },
    { label: 'Account Nickname', subMenu: true },
    { label: 'Status', subMenu: true },
    { label: 'Issued Date', subMenu: true },
  ];

  const renderSubMenu = (item) => {
    return (
      <Paper elevation={3} sx={{ p: 1, minWidth: 200 }}>
        {item !== 'Issued Date' && <TextField
          placeholder="Search"
          sx={styles.searchField}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" sx={styles.searchIconContainer}>
                <SearchIcon sx={styles.searchIcon} />
              </InputAdornment>
            ),
          }}
        />}
        {item === 'Bank account' && bankAccounts.map((account) => (
          <MenuItem key={account} sx={styles.subMenuItem} onClick={() => handleSubMenu(item, account)}>
            <Checkbox checked={(filterList.multiFilter['Bank account'] || []).includes(account)} sx={styles.checkbox} />
            <ListItemText primary={account} />
          </MenuItem>
        ))}
        {item === 'Payee Name' && payees.map((payee) => (
          <MenuItem key={payee} sx={styles.subMenuItem} onClick={() => handleSubMenu(item, payee)}>
            <Checkbox checked={(filterList.multiFilter['Payee Name'] || []).includes(payee)} sx={styles.checkbox} />
            <ListItemText primary={payee} />
          </MenuItem>
        ))}
        {item === 'Tags' && tags.map((tag) => (
          <MenuItem key={tag} sx={styles.subMenuItem} onClick={() => handleSubMenu(item, tag)}>
            <Checkbox checked={(filterList.multiFilter['Tags'] || []).includes(tag)} sx={styles.checkbox} />
            <ListItemText primary={tag} />
          </MenuItem>
        ))}

        {item === 'Account Nickname' && accountNicknames.map((accountNickname) => (
          <MenuItem key={accountNickname} sx={styles.subMenuItem} onClick={() => handleSubMenu(item, accountNickname)}>
            <Checkbox checked={(filterList.multiFilter['Account Nickname'] || []).includes(accountNickname)} sx={styles.checkbox} />
            <ListItemText primary={accountNickname} />
          </MenuItem>
        ))}

        {item === 'Status' && statuses.map((status) => (
          <MenuItem key={status} sx={styles.subMenuItem} onClick={() => handleSubMenu(item, status)}>
            <Checkbox checked={(filterList.multiFilter['Status'] || []).includes(status)} sx={styles.checkbox} />
            <ListItemText primary={status} />
          </MenuItem>
        ))}

        {item === 'Issued Date' && (
          <Box sx={{ minWidth: '360px', p: 1 }}>
            <Typography sx={styles.dateRangeTitle}>Date Range</Typography>
            <Divider sx={styles.divider} />
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("This Week")}>
              <Typography sx={styles.dateRangeText}>This Week</Typography>
              <Typography>{dateRanges.thisWeek.start} - {dateRanges.thisWeek.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("This Month")} >
              <Typography sx={styles.dateRangeText}>This Month</Typography>
              <Typography>{dateRanges.thisMonth.start} - {dateRanges.thisMonth.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("This Quarter")}>
              <Typography sx={styles.dateRangeText}>This Quarter</Typography>
              <Typography>{dateRanges.thisQuarter.start} - {dateRanges.thisQuarter.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("This Year")}>
              <Typography sx={styles.dateRangeText}>This Year</Typography>
              <Typography>{dateRanges.thisYear.start} - {dateRanges.thisYear.end}</Typography>
            </MenuItem>
            <Divider sx={styles.divider} />
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("Last Week")}>
              <Typography sx={styles.dateRangeText} >Last Week</Typography>
              <Typography>{dateRanges.lastWeek.start} - {dateRanges.lastWeek.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("Last Month")}>
              <Typography sx={styles.dateRangeText} >Last Month</Typography>
              <Typography>{dateRanges.lastMonth.start} - {dateRanges.lastMonth.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem}  onClick={() => handleDateRange("Last Quarter")}>
              <Typography sx={styles.dateRangeText}>Last Quarter</Typography>
              <Typography>{dateRanges.lastQuarter.start} - {dateRanges.lastQuarter.end}</Typography>
            </MenuItem>
            <MenuItem sx={styles.dateSubMenuItem} onClick={() => handleDateRange("Last Year")}>
              <Typography sx={styles.dateRangeText} >Last Year</Typography>
              <Typography>{dateRanges.lastYear.start} - {dateRanges.lastYear.end}</Typography>
            </MenuItem>
            <Divider sx={styles.divider} />
            <Typography sx={styles.dateRangeTitle}>Custom Range</Typography>

            <Box sx={styles.customDateContainer}>
              <Box sx={{ width: '50%' }}>
                <Typography sx={{ ...styles.dateRangeText, mb: 1 }}>From</Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={startDate}
                    onChange={(newValue) => setStartDate(newValue)}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth sx={styles.datePicker} />}
                    slotProps={{ textField: { sx: styles.datePicker } }}
                  />
                </LocalizationProvider>
              </Box>
              <Box sx={{ width: '50%' }}>
                <Typography sx={{ ...styles.dateRangeText, mb: 1 }}>To</Typography>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={endDate}
                    onChange={(newValue) => setEndDate(newValue)}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth sx={styles.datePicker} />}
                    slotProps={{ textField: { sx: styles.datePicker } }}
                  />
                </LocalizationProvider>
              </Box>
            </Box>

            <CustomButton
              variant="outlined"
              color="primary"
              sx={{ height: '32px', ml: 'auto' }}
              onClick={() => handleDateRange("Custom")}
            >
              Apply
            </CustomButton>

          </Box>
        )}
      </Paper>
    );
  };

  return (
    <Box sx={{ position: 'relative' }}>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        {menuItems.map((item) => (
          <Box
            key={item.label}
            sx={{ position: 'relative' }}
            onMouseEnter={(e) => handleHover(item.label, e)}
            onClick={(e) => handleHover(item.label, e)}
            onMouseLeave={handleLeave}
          >
            <MenuItem sx={styles.menuItem} onClick={() => handleMenuClick(item)}>
              {item.label}
              {item.subMenu && <KeyboardArrowRightIcon />}
            </MenuItem>
            {(item.label === 'Bank account' || item.label === 'Payee Name' || item.label === 'Tags' || item.label === 'Account Nickname' || item.label === 'Status' || item.label === 'Issued Date') && (
              <Popper
                open={hoveredItem === item.label}
                anchorEl={subAnchorEl}
                placement="right-start"
                style={{ zIndex: 1300 }}
              >

                <ClickAwayListener onClickAway={() => setHoveredItem(null)}>
                  {renderSubMenu(item.label)}
                </ClickAwayListener>
              </Popper>
            )}
          </Box>
        ))}
      </Menu>
    </Box>
  );
};

export default FilterMenu; 