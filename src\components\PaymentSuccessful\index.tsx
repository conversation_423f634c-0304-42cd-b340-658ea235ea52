import React from 'react';
import { Box, Typography, Button } from '@mui/material'
import { styles } from './style';
import { DownloadCheckIcon, PaymentDownloadIcon } from '../Icons';

export const PaymentSuccessful: React.FC<{}> = () => {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center', height: '100vh' }}>
      <Box sx={styles.alertIconContainer}>
        <Box sx={styles.alertIconBorder}>
          <Box sx={styles.alertIconMain}>
            <DownloadCheckIcon />
          </Box>
        </Box>
      </Box>

      <img src="/logo.svg" style={{ width: '200px' }} alt="logo" />

      <Typography sx={styles.title}>Payment Successful</Typography>

      <Typography sx={styles.description}>You have successfully paid</Typography>

      <Box sx={styles.boxInfo}>
        <Box sx={styles.itemContainer}>
          <Typography sx={{ fontSize: '18px', color: '#000000DE' }}>Payee Name</Typography>
          <Typography sx={{ fontSize: '16px', color: '#000000CC' }}>Robet Sabel</Typography>
        </Box>
        <Box sx={styles.itemContainer}>
          <Typography sx={{ fontSize: '18px', color: '#000000DE' }} >Amount</Typography>
          <Typography sx={{ fontSize: '16px', color: '#000000CC' }} >$220</Typography>
        </Box>
        <Box sx={styles.itemContainer}>
          <Typography sx={{ fontSize: '18px', color: '#000000DE' }} >Issued Date</Typography>
          <Typography sx={{ fontSize: '16px', color: '#000000CC' }} >2/21/2025</Typography>
        </Box>
      </Box>

      <Typography sx={styles.description}>Thank you for your payment!</Typography>

      <Button
        variant="contained"
        sx={{ ...styles.confirmButton, width: 'auto', gap: '10px', mt: '25px' }}
      >
        <PaymentDownloadIcon color="white" /> Download Receipt
      </Button>

    </Box>
  )
}