import React, { useState } from "react";
import {
  Box,
  IconButton,
  TextField,
  InputAdornment,
  Button,
} from "@mui/material";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import { styles } from "./styles";
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from "@mui/icons-material/Add";
import ActiveBankAccount from "./components/ActiveBankAccount";
import DeactivatedBankAccount from "./components/DeactivedBankAccount";
import { AddEditModal as AddNewModal } from "./components/modals";
import CustomTabs from "../../components/tabs";

const BankAccount = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [openAddNewModal, setOpenAddNewModal] = useState(false);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleHelpClick = () => {};

  return (
    <Box sx={styles.wrapper}>
      <Box sx={styles.tabContainer}>
        <CustomTabs
          activeTab={activeTab}
          handleTabChange={handleTabChange}
          styles={styles.tabs}
          tabs={[
            { label: "Bank Account" },
            { label: "Deactivated Bank Account" },
          ]}
        />
        <IconButton sx={styles.helpButton} onClick={handleHelpClick}>
          <HelpOutlineIcon />
        </IconButton>
      </Box>

      <Box sx={styles.actionContainer}>
        <TextField
          placeholder="Search"
          sx={{ ...styles.searchField }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" sx={styles.searchIconContainer}>
                <SearchIcon sx={styles.searchIcon} />
              </InputAdornment>
            ),
          }}
        />
        <Button
          sx={styles.nextButton}
          startIcon={<AddIcon />}
          onClick={() => setOpenAddNewModal(true)}
        >
          New
        </Button>
      </Box>

      {activeTab === 0 ? <ActiveBankAccount /> : <DeactivatedBankAccount />}

      <AddNewModal
        open={openAddNewModal}
        onClose={() => setOpenAddNewModal(false)}
        onConfirm={() => setOpenAddNewModal(false)}
      />
    </Box>
  );
};

export default BankAccount;
