import { useRef } from "react";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { Box, Typography, Button, Select, MenuItem, TextField, Radio } from "@mui/material";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { Formik } from "formik";
import * as Yup from "yup";
import { styles } from "../../../styles";

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const BlankCheckModal = ({ open, onClose, handleOpenUnsavedAlert, checkData, handleOpenDownloadCheckAlert, setOpenGeneratingCheckDialog }) => {
  const formRef = useRef(null);
  const type = checkData ? 'edit' : 'add';
  const initialValues = {
    bankAccount: checkData?.bankAccount || "",
    checkNumber: checkData?.no || "2051",
    signature: checkData?.signature || true,
    numberOfChecks: checkData?.numberOfChecks || 1,
  }
  const validationSchema = Yup.object().shape({
    bankAccount: Yup.string().required("Bank Account is required"),
    checkNumber: Yup.string().required("Check Number is required"),
    numberOfChecks: Yup.string().required("Number of Checks is required"),
    signature: Yup.boolean().required("Signature is required"),
  })

  const handleSaveAndPrint= () => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    try {

      console.log(values);
      onClose();
      setOpenGeneratingCheckDialog(true);
      // If editing existing check
      if (checkData) {
        // Update existing check
        console.log('Updating check:', values);
        await sleep(2000);
        // Add your API call here
      } else {
        // Add new check
        console.log('Adding new check:', values);
        await sleep(2000);
        // Add your API call here
      }

      // Reset form and close modal
      resetForm();

      // Optional: Show success message
      // You can add a toast or snackbar here

    } catch (error) {
      console.error('Error submitting check:', error);
      // Handle error - show error message to user
    } finally {
      handleOpenDownloadCheckAlert();
      setSubmitting(false);
    }
  };
  return (

    <CustomDialog
      open={open}
      onClose={() => {
        handleOpenUnsavedAlert();
      }}
      width="760px"
      title={type === 'edit' ? 'Enter Check number' : 'Create Blank Check'}
      content={
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          innerRef={formRef}
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
            <Box>
              <Typography color="#00000099" sx={{ mb: "24px" }}>
                Following Fields are required to create a blank check.
              </Typography>

              <Box sx={styles.addEditBlankContainer}>
                <Box sx={{ mb: '24px' }}>
                  <Typography sx={styles.checkTitle}>
                    Your Signature
                  </Typography>
                  <Typography sx={styles.checkSubTitle}>
                    Want to sign with signature
                  </Typography>

                  <Box sx={styles.radioGroup}>
                    <Box sx={styles.radioItem}>
                      <Radio
                        name="signature"
                        value={true}
                        checked={values.signature === true}
                        onChange={(e) => {
                          setFieldValue("signature", true);
                        }}
                        sx={styles.radio}
                      />
                      <Typography sx={styles.radioLabel}>Yes</Typography>
                    </Box>
                    <Box sx={styles.radioItem}>
                      <Radio
                        name="signature"
                        value={false}
                        checked={values.signature === false}
                        onChange={(e) => {
                          setFieldValue("signature", false);
                        }}
                        sx={styles.radio}
                      />
                      <Typography sx={styles.radioLabel}>No</Typography>
                    </Box>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', gap: '24px', mb: '24px' }}>
                  <Box sx={{ width: '50%' }}>
                    <Typography sx={styles.balankItemLabel}>
                      Bank Account *
                    </Typography>
                    <Box>
                      <Select
                        fullWidth
                        name="bankAccount"
                        value={values.bankAccount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        displayEmpty
                        renderValue={(selected) => {
                          if (!selected) {
                            return (
                              <Typography sx={{ color: "text.secondary" }}>
                                Select Bank Account
                              </Typography>
                            );
                          }
                          return selected;
                        }}
                        error={touched.bankAccount && !!errors.bankAccount}
                        sx={{ ...styles.select, width: '100%' }}
                      >
                        <MenuItem value="JP Morgan Chase.">
                          JP Morgan Chase.
                        </MenuItem>
                      </Select>
                      {touched.bankAccount && errors.bankAccount && (
                        <Typography color="error" sx={styles.errorText}>
                          {errors.bankAccount}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  <Box sx={{ width: '50%' }}>
                    <Typography sx={styles.balankItemLabel}>
                      Number of checks
                    </Typography>
                    <TextField
                      fullWidth
                      name="numberOfChecks"
                      placeholder="Number of checks"
                      value={values.numberOfChecks}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.numberOfChecks && !!errors.numberOfChecks}
                      helperText={touched.numberOfChecks && errors.numberOfChecks}
                      sx={{ ...styles.input, width: '100%' }}
                    />
                  </Box>
                </Box>
                <Box>
                  <Typography sx={styles.balankItemLabel}>
                    Check Number
                  </Typography>
                  <TextField
                    fullWidth
                    name="checkNumber"
                    placeholder="Check Number"
                    value={values.checkNumber}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.checkNumber && !!errors.checkNumber}
                    helperText={touched.checkNumber && errors.checkNumber}
                    sx={{
                      ...styles.input, width: '100%', "& .MuiInputBase-input": {
                        color: "#CECECE",
                      },
                      "& .MuiInputLabel-root": {
                        color: "#CECECE",
                      },
                    }}
                  />
                </Box>
              </Box>
            </Box>
          )}
        </Formik>
      }
      actions={
        <Box sx={styles.actionsContainer}>
          <Button
            onClick={() => {
              handleOpenUnsavedAlert();
            }}
            variant="outlined"
            sx={styles.cancelButton}
          >
            Cancel
          </Button>

          <CustomButton
            variant="outlined"
            color="primary"
            onClick={handleSaveAndPrint}
          >
            Save & Print
          </CustomButton>
        </Box>
      }
    />
  );
};
