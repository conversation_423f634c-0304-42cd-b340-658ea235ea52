import { useEffect, useState } from "react";
import { CustomTable } from "../../../../components/table/CustomTable";
import { Box, Button, Checkbox, Typography, useTheme } from "@mui/material";
import { styles } from "../../styles";
import { getStatusColor } from "../../index";

const mockChecks = [
  {
    id: 1,
    no: 1,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "ABC Corp",
    issuedDate: "2/12/2025",
    status: "Processing",
    amount: "$1.45",
  },
  {
    id: 2,
    no: 2,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "ABC Corp",
    issuedDate: "2/12/2025",
    status: "Submitted",
    amount: "$1.45",
  },
  {
    id: 3,
    no: 3,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "-",
    issuedDate: "2/12/2025",
    status: "Mailed",
    amount: "$1.45",
  },
  {
    id: 4,
    no: 4,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "-",
    issuedDate: "2/12/2025",
    status: "Cancelled",
    amount: "$1.45",
  },
  {
    id: 5,
    no: 5,
    payeeName: "<PERSON>bel",
    account: "<PERSON>oe123",
    org: "XYZ Inc",
    issuedDate: "2/12/2025",
    status: "Mailed",
    amount: "$1.45",
  },
];

export const AllCheckMailList = ({ search = "all", setData }) => {
  const theme = useTheme();
  const [checks, setChecks] = useState(mockChecks);
  const [selectedChecks, setSelectedChecks] = useState([]);
  const [isCheckedAll, setIsCheckedAll] = useState(false);


  useEffect(() => {
    if (checks.length > 0) {
      setIsCheckedAll(selectedChecks.length === checks.length);
      setData(selectedChecks);
    }
  }, [selectedChecks, checks.length]);

  const handleSelectAll = (event) => {
    const checked = event.target.checked;
    setIsCheckedAll(checked);

    if (checked) {
      setSelectedChecks(checks);
    } else {
      setSelectedChecks([]);
    }
  };

  const renderHeaderCell = (column) => {
    switch (column.id) {
      case "selected":
        return (
          <Checkbox
            checked={isCheckedAll}
            onChange={handleSelectAll}
            sx={styles.checkbox}
          />
        );
      default:
        return column.label;
    }
  };

  const renderCell = (row, column) => {
    switch (column.id) {
      case "selected":
        return (
          <Checkbox
            checked={selectedChecks.some((check) => check.id === row.id)}
            onChange={() => handleSelectCheck(row.id)}
            sx={styles.checkbox}
          />
        );
      case "status":
        return (
          <Button
            size="small"
            sx={{
              width: "110px",
              py: '8px',
              fontSize: '12px',
              borderRadius: '5px',
              textTransform: 'none',
              lineHeight: '100%',
              color: getStatusColor(row.status),
              backgroundColor: `${getStatusColor(row.status)}0D`,
              border: `1px solid ${getStatusColor(row.status)}`,
              "&:hover": {
                backgroundColor: `${getStatusColor(row.status)}1A`,
                border: `1px solid ${getStatusColor(row.status)}`,
              },
            }}
          >
            {row.status}
          </Button>
        );
      case "action":
        return <Box sx={{display: 'flex', gap: '10px', justifyContent: 'center'}}>
          {search === "error" && <Typography sx={styles.actionRetry}>Retry</Typography>}
          <Typography sx={styles.actionCancel} >Cancel</Typography>
          </Box>
      default:
        return row[column.id];
    }
  };

  const handleSelectCheck = (checkId) => {
    setSelectedChecks((prev) => {
      // Find the check object
      const check = checks.find((c) => c.id === checkId);
      const isSelected = prev.some(
        (selectedCheck) => selectedCheck.id === checkId
      );

      if (isSelected) {
        return prev.filter((selectedCheck) => selectedCheck.id !== checkId);
      } else {
        return [...prev, check];
      }
    });
  };

  return (
    <>
      <CustomTable
        columns={
          search === "all"
            ? [
              { id: "selected", label: "", width: "40px" },
              { id: "no", label: "Check No.", width: "100px" },
              { id: "payeeName", label: "Payee Name", width: "220px" },
              { id: "account", label: "User Account", width: "220px" },
              { id: "org", label: "Organization", width: "200px" },
              { id: "issuedDate", label: "Issued Date", width: "140px" },
              { id: "status", label: "Status", width: "120px" },
            ]
            : [
              { id: "selected", label: "", width: "40px" },
              { id: "no", label: "Check No.", width: "100px" },
              { id: "payeeName", label: "Payee Name", width: "220px" },
              { id: "account", label: "User Account", width: "220px" },
              { id: "org", label: "Organization", width: "200px" },
              { id: "issuedDate", label: "Issued Date", width: "140px" },
              { id: "action", label: "Actions", width: '120px'}
            ]
        }
        data={checks}
        renderCell={renderCell}
        renderHeaderCell={renderHeaderCell}
        isCenteredCells={true}
      />
    </>
  );
};
