import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { styles } from './styles';

interface Column {
  id: string;
  label: string;
  width?: string;
}

interface CustomTableProps<T = any> {
  columns: Column[];
  data: T[];
  renderCell?: any;
  renderHeaderCell?: any;
  isCenteredCells?: boolean;
  onClickRow?: (row: T) => void;
}

export const CustomTable = <T extends { id: string | number }>({
  columns,
  data,
  renderCell,
  renderHeaderCell,
  isCenteredCells = false,
  onClickRow
}: CustomTableProps<T>) => {
  return (
    <TableContainer sx={styles.container}>
      <Table>
        <TableHead>
          <TableRow sx={styles.headerRow}>
            {columns.map((column) => (
              <TableCell 
                key={`table-header-${column.id}`} 
                width={column.width} 
                align={isCenteredCells ? 'center' : 'left'}
              >
                {renderHeaderCell ? renderHeaderCell(column) : column.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row: any) => (
            <TableRow
              key={row.id}
              sx={{...styles.bodyRow, backgroundColor: row.id % 2 ? 'transparent' : '#F9FAFB'}}
              onClick={onClickRow ? () => onClickRow(row) : undefined}
              hover={!!onClickRow}
              style={onClickRow ? { cursor: 'pointer' } : undefined}
            >
              {columns.map((column) => (
                <TableCell 
                  key={`${row.id}-${column.id}`} 
                  sx={{wordBreak: 'break-all'}} 
                  align={isCenteredCells ? 'center' : 'left'}
                >
                  {renderCell ? renderCell(row, column) : row[column.id as keyof T]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default CustomTable;
