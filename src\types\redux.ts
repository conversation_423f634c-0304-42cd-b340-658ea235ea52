/**
 * Redux TypeScript types and interfaces
 * 
 * This file contains type definitions for your Redux store, actions, and state.
 * Use these types to ensure type safety throughout your Redux implementation.
 */

import { ThunkAction, Action } from '@reduxjs/toolkit';
import { User, Check, BankAccount, Notification } from './index';

// Root State Interface
// Add your slice states here as you convert them to TypeScript
export interface RootState {
  alert: AlertState;
  auth: AuthState;
  checks: ChecksState;
  banks: BanksState;
  ui: UIState;
  // Add more slices as needed
}

// Alert Slice State
export interface AlertState {
  showAlert: boolean;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

// Auth Slice State
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  token: string | null;
  refreshToken: string | null;
  loginAttempts: number;
  lastLoginTime: string | null;
}

// Checks Slice State
export interface ChecksState {
  checks: Check[];
  currentCheck: Check | null;
  loading: boolean;
  error: string | null;
  filters: CheckFilters;
  pagination: PaginationState;
  searchQuery: string;
  sortBy: keyof Check;
  sortOrder: 'asc' | 'desc';
}

// Banks Slice State
export interface BanksState {
  accounts: BankAccount[];
  currentAccount: BankAccount | null;
  loading: boolean;
  error: string | null;
  selectedAccountId: string | null;
}

// UI Slice State
export interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  modals: {
    editCheck: boolean;
    deleteCheck: boolean;
    addBank: boolean;
    userProfile: boolean;
  };
  loading: {
    global: boolean;
    checks: boolean;
    banks: boolean;
    auth: boolean;
  };
}

// Filter Types
export interface CheckFilters {
  status?: Check['status'][];
  dateFrom?: string;
  dateTo?: string;
  payee?: string;
  bankAccountId?: string;
  amountMin?: number;
  amountMax?: number;
}

// Pagination State
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Action Types
export interface AsyncActionState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

// Thunk Types
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;

export type AppDispatch = any; // You can make this more specific based on your store setup

// Action Payload Types
export interface LoginPayload {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface CreateCheckPayload {
  amount: number;
  payee: string;
  memo?: string;
  bankAccountId: string;
  date: string;
}

export interface UpdateCheckPayload {
  id: string;
  updates: Partial<Omit<Check, 'id' | 'createdAt' | 'updatedAt'>>;
}

export interface AddBankAccountPayload {
  accountName: string;
  accountNumber: string;
  routingNumber: string;
  bankName: string;
  accountType: 'checking' | 'savings';
}

// Selector Return Types
export type ChecksSelector = (state: RootState) => Check[];
export type CurrentCheckSelector = (state: RootState) => Check | null;
export type AuthUserSelector = (state: RootState) => User | null;
export type IsAuthenticatedSelector = (state: RootState) => boolean;
export type LoadingSelector = (state: RootState) => boolean;
export type ErrorSelector = (state: RootState) => string | null;

// Redux Toolkit Query Types (if you're using RTK Query)
export interface ApiError {
  status: number;
  data: {
    message: string;
    errors?: Record<string, string[]>;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: PaginationState;
}

// Middleware Types
export interface SerializedError {
  name?: string;
  message?: string;
  stack?: string;
  code?: string;
}

// Store Configuration Types
export interface StoreConfig {
  preloadedState?: Partial<RootState>;
  middleware?: any[];
  devTools?: boolean;
}

// Action Creator Types
export interface ActionCreator<P = void> {
  (payload: P): Action<string> & { payload: P };
  type: string;
}

export interface AsyncActionCreator<P = void, R = any> {
  pending: ActionCreator<void>;
  fulfilled: ActionCreator<R>;
  rejected: ActionCreator<SerializedError>;
  (payload: P): AppThunk<Promise<R>>;
}

// Utility Types for Redux
export type StateSlice<T> = {
  [K in keyof T]: T[K];
};

export type ActionPayload<T> = T extends ActionCreator<infer P> ? P : never;

// Example of how to type your slice reducers
export interface SliceReducers<State> {
  [key: string]: (state: State, action: any) => State | void;
}

// Example of how to type your extra reducers
export interface ExtraReducers<State> {
  [key: string]: (state: State, action: any) => State | void;
}

/**
 * USAGE EXAMPLES:
 * 
 * 1. In your components:
 *    const user = useSelector((state: RootState) => state.auth.user);
 * 
 * 2. In your slice files:
 *    const initialState: AuthState = {
 *      user: null,
 *      isAuthenticated: false,
 *      // ...
 *    };
 * 
 * 3. In your thunks:
 *    export const loginUser = createAsyncThunk<User, LoginPayload>(
 *      'auth/loginUser',
 *      async (payload) => {
 *        // implementation
 *      }
 *    );
 * 
 * 4. In your selectors:
 *    export const selectUser: AuthUserSelector = (state) => state.auth.user;
 */
