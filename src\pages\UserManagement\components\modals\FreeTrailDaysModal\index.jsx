import React, {useEffect, useState} from 'react'
import { CustomDialog } from '../../../../../components/dialog/CustomDialog'
import { CustomButton } from '../../../../../components/buttons/CustomButton'
import { Box, Button, InputAdornment, TextField, Typography, Popover } from '@mui/material'
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { format, addDays, differenceInDays } from 'date-fns'
import { styles } from '../../../styles'
import { formatAmountAsCurrency } from '../../../../../common/utils'

export const FreeTrailDaysModal = ({ open, onClose, user }) => {
  const [freeTrailDays, setFreeTrailDays] = useState(user?.freeTrailDays || '');
  const [endDate, setEndDate] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    setFreeTrailDays(user?.freeTrailDays || '');
    // If user has free trial days, calculate the end date
    if (user?.freeTrailDays) {
      setEndDate(addDays(new Date(), parseInt(user.freeTrailDays)));
    }
  }, [user]);

  // Update end date when free trial days change
  const handleDaysChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setFreeTrailDays(value);
    
    if (value && !isNaN(parseInt(value))) {
      const days = parseInt(value);
      setEndDate(addDays(new Date(), days));
    } else {
      setEndDate(null);
    }
  };

  // Update free trial days when end date changes
  const handleDateChange = (newDate) => {
    setEndDate(newDate);
    
    if (newDate) {
      const today = new Date();
      const daysDiff = differenceInDays(newDate, today);
      setFreeTrailDays(daysDiff > 0 ? daysDiff.toString() : '0');
    }
    
    handleDateClose();
  };

  const handleSave = () => {
    console.log(user, freeTrailDays, endDate);
    onClose();
  };

  const handleDateClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleDateClose = () => {
    setAnchorEl(null);
  };

  const openDatePicker = Boolean(anchorEl);

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      width={685}
      title={user?.freeTrailDays ? "Edit Free Trail Days" : "Add Free Trail Days"}
      content={<Box>
        <Typography sx={styles.lable}>Free Trail days</Typography>
        <TextField
          fullWidth
          name="days"
          placeholder="Days"
          value={freeTrailDays}
          onChange={handleDaysChange}
          type="text"
          inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
          sx={{...styles.input, width: '300px' , mb: 1}}
        />

        <Typography sx={{color: '#00000099', display: 'flex', gap : 1}}>
          End
          <Typography 
            onClick={handleDateClick}
            sx={{
              textDecorationLine: 'underline', 
              fontWeight: '600', 
              color: '#204464',
              cursor: 'pointer'
            }}
          >
            {endDate ? format(endDate, 'MMM dd, yyyy') : 'Date'}
          </Typography>
        </Typography>
        
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Popover
            open={openDatePicker}
            anchorEl={anchorEl}
            onClose={handleDateClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <StaticDatePicker
              displayStaticWrapperAs="desktop"
              value={endDate}
              onChange={handleDateChange}
              renderInput={() => null}
              minDate={addDays(new Date(), 1)}
            />
          </Popover>
        </LocalizationProvider>

      </Box>}
      actions={
        <>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={styles.cancelButton}
          >
            Cancel
          </Button>
          <CustomButton
            variant="outlined"
            color="primary"
            onClick={handleSave}
          >
            Save Edit
          </CustomButton>
        </>
      }
    />
  )
}
