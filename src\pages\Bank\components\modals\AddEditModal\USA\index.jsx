import React, { useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  Radio,
  Switch,
  Tooltip,
} from "@mui/material";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { SignaturePad } from "../../../../../../components/signaturePad";
import { styles } from "../../../../styles";
import { ErrorIcon } from "../../../../../../components/Icons";

const validationSchema = Yup.object().shape({
  routingNumber: Yup.string()
    .matches(/^\d{9}$/, "Routing number must be exactly 9 digits")
    .required("Routing number cannot be empty."),
  confirmRoutingNumber: Yup.string()
    .oneOf(
      [Yup.ref("routingNumber"), null],
      "Routing Number and Confirm Routing Number do not match."
    )
    .required("Confirm routing number cannot be empty."),
  accountNumber: Yup.string()
    .matches(/^\d{4,17}$/, "Account number must be between 4 and 17 digits")
    .required("Account number cannot be empty."),
  confirmAccountNumber: Yup.string()
    .oneOf(
      [Yup.ref("accountNumber"), null],
      "Account Number and Confirm Account Number do not match."
    )
    .required("Confirm account number cannot be empty."),
  accountType: Yup.string().required("Please select an account type."),
  bankName: Yup.string().required("Bank name cannot be empty."),
  nickName: Yup.string().required(
    "Name cannot be empty or contain special characters."
  ),
  checkNoGeneration: Yup.string()
    .oneOf(["manual", "auto"])
    .required("Please select check number generation method"),
  signatureEnabled: Yup.boolean(),
  signature: Yup.string().when("signatureEnabled", {
    is: true,
    then: () => Yup.string().required("Signature is required when enabled"),
    otherwise: () => Yup.string().nullable(),
  }),
});

const initialUSAValues = {
  routingNumber: "",
  confirmRoutingNumber: "",
  accountNumber: "",
  confirmAccountNumber: "",
  accountType: "",
  bankName: "",
  nickName: "",
  checkNoGeneration: "manual", // or "auto" as default
  checkNoLength: "",
  checkNoStarting: "",
  signature: null,
  signatureEnabled: false,
};

export const AddNewModalUSA = ({
  formRef,
  onSuccess,
  editMode = false,
  data = null,
}) => {
  const [initialValues, setInitialValues] = useState(
    editMode
      ? {
        routingNumber: data && data.routingNumber,
        confirmRoutingNumber: data && data.confirmRoutingNumber,
        accountNumber: data && data.accountNumber,
        confirmAccountNumber: data && data.confirmAccountNumber,
        accountType: data && data.accountType,
        bankName: data && data.bankName,
        nickName: data && data.nickName,
        checkNoGeneration: data && data.checkNoGeneration, // or "auto" as default
        checkNoLength: data && data.checkNoLength,
        checkNoStart: data && data.checkNoStart,
        signature: data && data.signature,
        signatureEnabled: data && data.signatureEnabled,
      }
      : initialUSAValues
  );
  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      // Here you would typically make an API call to save the bank account
      const formData = new FormData();

      // Add all form values to formData
      Object.keys(values).forEach((key) => {
        if (key !== "signature") {
          formData.append(key, values[key]);
        }
      });

      // Add signature if enabled and exists
      if (values.signatureEnabled && values.signature) {
        // Convert base64 to blob
        const base64Response = await fetch(values.signature);
        const blob = await base64Response.blob();
        formData.append("signature", blob, "signature.png");
      }

      // Make your API call here
      if (editMode) {
        console.log("editMode");
        // await api.put('/bank-accounts', formData);
      }
      else {
        console.log("addMode");
        // await api.post('/bank-accounts', formData);
      }

      console.log("Submitting USA bank account:", values);
      onSuccess();
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      innerRef={formRef}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        setFieldValue,
      }) => (
        <Form>
          <Box sx={styles.formContainer}>
            <Box sx={styles.groupContainer}>
              <Box sx={styles.inputGroup}>
                <Typography sx={styles.inputLabel}>Routing Number*</Typography>
                <TextField
                  fullWidth
                  name="routingNumber"
                  placeholder="Routing Number"
                  value={values.routingNumber}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.routingNumber && !!errors.routingNumber}
                  helperText={touched.routingNumber && errors.routingNumber}
                  sx={styles.input}
                />
              </Box>
              <Box sx={styles.inputGroup}>
                <Typography sx={styles.inputLabel}>
                  Confirm Routing Number*
                </Typography>
                <TextField
                  fullWidth
                  name="confirmRoutingNumber"
                  placeholder="ConfirmRouting Number"
                  value={values.confirmRoutingNumber}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={
                    touched.confirmRoutingNumber &&
                    !!errors.confirmRoutingNumber
                  }
                  helperText={
                    touched.confirmRoutingNumber && errors.confirmRoutingNumber
                  }
                  sx={styles.input}
                />
              </Box>
            </Box>

            <Box sx={styles.groupContainer}>
              <Box sx={styles.inputGroup}>
                <Box sx={styles.labelContainer}>
                  <Typography sx={styles.inputLabel}>Bank Name*</Typography>
                  <Tooltip
                    title="Enter the official name of the bank as it should appear on printed checks."
                    placement="top-end"
                    arrow
                    componentsProps={{
                      tooltip: {
                        sx: {
                          ...styles.tooltip,
                          width: "185px",
                        },
                      },
                      popper: {
                        sx: {
                          marginBottom: "8px !important",
                        },
                      },
                    }}
                  >
                    <Box sx={styles.labelIcon}>
                      <HelpOutlineIcon />
                    </Box>
                  </Tooltip>
                </Box>
                <TextField
                  fullWidth
                  name="bankName"
                  placeholder="JP Morgan Chase"
                  value={values.bankName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.bankName && !!errors.bankName}
                  helperText={touched.bankName && errors.bankName}
                  sx={styles.input}
                />
              </Box>
              <Box sx={styles.inputGroup}>
                <Typography sx={styles.inputLabel}>
                  Select Account Type*
                </Typography>
                <Select
                  fullWidth
                  name="accountType"
                  value={values.accountType}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  displayEmpty
                  renderValue={(selected) => {
                    if (!selected) {
                      return (
                        <Typography sx={{ color: "text.secondary" }}>
                          Select Account Type
                        </Typography>
                      );
                    }
                    return selected;
                  }}
                  error={touched.accountType && !!errors.accountType}
                  sx={styles.select}
                >
                  <MenuItem value="Personal Checking">
                    Personal Checking
                  </MenuItem>
                  <MenuItem value="Personal Savings">Personal Savings</MenuItem>
                  <MenuItem value="Business Checking">
                    Business Checking
                  </MenuItem>
                  <MenuItem value="Business Savings">Business Savings</MenuItem>
                </Select>
                {touched.accountType && errors.accountType && (
                  <Typography color="error" sx={styles.errorText}>
                    {errors.accountType}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box sx={styles.groupContainer}>
              <Box sx={styles.inputGroup}>
                <Typography sx={styles.inputLabel}>Account Number*</Typography>
                <TextField
                  fullWidth
                  name="accountNumber"
                  placeholder="Account Number"
                  value={values.accountNumber}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.accountNumber && !!errors.accountNumber}
                  helperText={touched.accountNumber && errors.accountNumber}
                  sx={styles.input}
                />
              </Box>
              <Box sx={styles.inputGroup}>
                <Typography sx={styles.inputLabel}>
                  Confirm Account Number*
                </Typography>
                <TextField
                  fullWidth
                  name="confirmAccountNumber"
                  placeholder="Confirm Account Number"
                  value={values.confirmAccountNumber}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={
                    touched.confirmAccountNumber &&
                    !!errors.confirmAccountNumber
                  }
                  helperText={
                    touched.confirmAccountNumber && errors.confirmAccountNumber
                  }
                  sx={styles.input}
                />
              </Box>
            </Box>

            {true && (
              <Box sx={styles.errorContainer}>
                <ErrorIcon />
                <Typography sx={styles.errorAlertText}>
                  An account with this account number and transit number already exists in the system. Please use a different Number for this account.
                </Typography>
              </Box>
            )}

            <Box sx={styles.inputGroup}>
              <Box sx={styles.labelContainer}>
                <Typography sx={styles.inputLabel}>
                  Account Nickname*
                </Typography>
                <Tooltip
                  title="Enter a custom name for this bank account. This nickname is for your reference only and will not appear on printed checks."
                  placement="top-end"
                  arrow
                  componentsProps={{
                    tooltip: {
                      sx: {
                        ...styles.tooltip,
                      },
                    },
                    popper: {
                      sx: {
                        marginBottom: "8px !important",
                      },
                    },
                  }}
                >
                  <Box sx={styles.labelIcon}>
                    <HelpOutlineIcon />
                  </Box>
                </Tooltip>
              </Box>
              <TextField
                fullWidth
                name="nickName"
                placeholder="My Chase Account"
                value={values.nickName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.nickName && !!errors.nickName}
                helperText={touched.nickName && errors.nickName}
                sx={styles.input}
              />
            </Box>

            <Typography sx={styles.inputLabel}>Preferences</Typography>
            <Typography color="text.secondary">
              Change Your Checkwriting Preferences here.
            </Typography>

            <Box sx={styles.inputGroup}>
              <Typography sx={styles.inputLabel}>
                Check No. generation
              </Typography>
              <Box sx={styles.radioGroup}>
                <Box sx={styles.radioItem}>
                  <Radio
                    name="checkNoGeneration"
                    value="manual"
                    checked={values.checkNoGeneration === "manual"}
                    onChange={handleChange}
                    sx={styles.radio}
                  />
                  <Typography sx={styles.radioLabel}>Manual</Typography>
                </Box>
                <Box sx={styles.radioItem}>
                  <Radio
                    name="checkNoGeneration"
                    value="auto"
                    checked={values.checkNoGeneration === "auto"}
                    onChange={handleChange}
                    sx={styles.radio}
                  />
                  <Typography sx={styles.radioLabel}>Auto</Typography>
                </Box>
              </Box>
            </Box>

            <Box sx={styles.groupContainer}>
              <Box sx={styles.inputGroup}>
                <Box sx={styles.labelContainer}>
                  <Typography sx={styles.inputLabel}>
                    Length of check number
                  </Typography>
                  <Tooltip
                    title="Choose the number of digits for check numbers. Shorter numbers will be padded with zeros."
                    placement="top-end"
                    arrow
                    componentsProps={{
                      tooltip: {
                        sx: {
                          ...styles.tooltip,
                        },
                      },
                      popper: {
                        sx: {
                          marginBottom: "8px !important",
                        },
                      },
                    }}
                  >
                    <Box sx={styles.labelIcon}>
                      <HelpOutlineIcon />
                    </Box>
                  </Tooltip>
                </Box>
                <TextField
                  fullWidth
                  name="checkNoLength"
                  placeholder="0001"
                  value={values.checkNoLength}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.checkNoLength && !!errors.checkNoLength}
                  helperText={touched.checkNoLength && errors.checkNoLength}
                  sx={styles.input}
                />
              </Box>
              <Box sx={styles.inputGroup}>
                {values.checkNoGeneration === "auto" && (
                  <>
                    <Typography sx={styles.inputLabel}>
                      Default check start number
                    </Typography>
                    <TextField
                      fullWidth
                      name="checkNoStart"
                      placeholder="1000"
                      value={values.checkNoStart}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.checkNoStart && !!errors.checkNoStart}
                      helperText={touched.checkNoStart && errors.checkNoStart}
                      sx={styles.input}
                    />
                  </>
                )}
              </Box>
            </Box>
            {false && (
              <Typography
                color="error"
                sx={{ ...styles.errorText, marginTop: "-10px" }}
              >
                This check number is already used. Try 1002 instead.
              </Typography>
            )}

            <Box sx={styles.signatureContainer}>
              <Box sx={styles.signatureLabelContainer}>
                <Box>
                  <Typography sx={styles.signatureLabel}>
                    Your Signature
                  </Typography>
                  <Typography color="text.secondary">
                    Enable signature for every check, If Uploaded
                  </Typography>
                </Box>
                <Switch
                  checked={values.signatureEnabled}
                  onChange={(e) => {
                    setFieldValue("signatureEnabled", e.target.checked);
                    if (!e.target.checked) {
                      setFieldValue("signature", null);
                    }
                  }}
                  sx={styles.switch}
                />
              </Box>

              {values.signatureEnabled && (
                <>
                  <SignaturePad
                    value={values.signature}
                    onChange={(signatureData) => {
                      setFieldValue("signature", signatureData);
                    }}
                  />
                  {touched.signature && errors.signature && (
                    <Typography color="error" sx={styles.errorText}>
                      {errors.signature}
                    </Typography>
                  )}
                </>
              )}
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
};
