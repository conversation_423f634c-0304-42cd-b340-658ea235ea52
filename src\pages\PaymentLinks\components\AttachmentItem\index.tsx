import React from 'react';
import { Box, Typography } from '@mui/material';
import { getFileIcon, formatFileSize } from '../../../../common/fileUtils';
import { styles } from '../../../MyChecks/styles';

interface Attachment {
  id?: string;
  name: string;
  size?: number;
  description?: string;
  type?: string;
  url?: string;
}

interface AttachmentItemProps {
  attachment: Attachment;
}

export const AttachmentItem: React.FC<AttachmentItemProps> = ({ attachment }) => {
  return (
    <Box sx={styles.attachmentMenuItem}>
      <Box sx={styles.attachmentIconContainer}>
        {getFileIcon(attachment)}
      </Box>
      <Box>
        <Typography sx={{ fontSize: '12px', color: '#00000099' }}>
          {attachment.name}
        </Typography>
        <Typography sx={{ fontSize: '10px', color: '#00000099' }}>
          {attachment.description || formatFileSize(attachment.size || 0)}
        </Typography>
      </Box>
    </Box>
  );
};

export default AttachmentItem;