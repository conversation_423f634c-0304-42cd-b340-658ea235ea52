import React, { useState } from "react";
import {
  Box,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  Button,
  Pagination,
  PaginationItem,
  Tooltip,
  Checkbox,
} from "@mui/material";
import { CustomTable } from "../../components/table/CustomTable";
import { CustomButton } from "../../components/buttons/CustomButton";
import SearchIcon from "@mui/icons-material/Search";
import { styles } from "./styles";

const PaymentLink: React.FC = () => {

  return (
    <Box sx={styles.wrapper}>
      <Typography sx={styles.title}>
        My Payment Links
      </Typography>

      <Typography
        variant="body1"
        color="text.secondary"
        sx={styles.description}
      >
        Below is the list of all your Payment links
      </Typography>
    </Box>
  )
}

export default PaymentLink;
