import React, { useState } from "react";
import {
  Box,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  Button,
  Pagination,
  PaginationItem,
  Tooltip,
  Checkbox,
} from "@mui/material";
import { FilterIcon } from '@/components/Icons';

import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { CustomTable } from "@/components/table/CustomTable";
import { CustomButton } from "@/components/buttons/CustomButton";
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from '@mui/icons-material/Add';
import { ReactClickHandler } from '@/types/react'

import { styles } from "./styles";

const PaymentLink: React.FC = () => {
  // Use proper typing for the anchor element state
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState<HTMLElement | null>(null);

  return (
    <Box sx={styles.wrapper}>
      <Box sx={styles.titleContainer} >
        <Box>
          <Typography sx={styles.title}>
            My Payment Links
          </Typography>

          <Typography
            variant="body1"
            color="text.secondary"
            sx={styles.description}
          >
            Below is the list of all your Payment links
          </Typography>
        </Box>
        <Box>
          <HelpOutlineIcon sx={styles.helpIcon} />
        </Box>
      </Box>

      <Box sx={styles.actionContainer}>
        <CustomButton
          variant="outlined"
          endIcon={<FilterIcon color="#1a3850" />}
          color="secondary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => setFilterMenuAnchorEl(e.currentTarget)}
        >
          Filter
        </CustomButton>

        <CustomButton
          variant="outlined"
          startIcon={<AddIcon />}
          color="primary"
          onClick={() => {
            // Add your logic here
          }}
        >
          New
        </CustomButton>
      </Box>

    </Box>
  )
}

export default PaymentLink;
