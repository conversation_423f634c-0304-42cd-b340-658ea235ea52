import React, { useState } from "react";
import {
  <PERSON>,
  Typography,
  IconButton,
  Pagination,
  PaginationItem,
  Menu,
  MenuItem,
  Popper,
  Paper,
  Fade,
  ListItemText
} from "@mui/material";
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import AddIcon from '@mui/icons-material/Add';
import LinkIcon from '@mui/icons-material/Link';
import { CustomTable } from "../../components/table/CustomTable";
import { CustomButton } from "../../components/buttons/CustomButton";
import { FilterIcon, MoreVerticalIcon, PaymentCancelIcon, PaymentResendIcon, PaymentCopyLinkIcon, PaymentDownloadIcon } from '../../components/Icons';
import FilterMenu from "./components/FilterMenu";
import { AttachmentsModal } from "./components/modals/AttachmentsModal";
import { AttachmentItem } from './components/AttachmentItem';
import { CancelPaymentLinkAlert } from './components/modals/CancelPaymentLinkAlert'
import { CreatePaymentLinkModal } from './components/modals/CreatePaymentLinkModal'
import { styles } from "./styles";

// Define proper interfaces for the data
interface Attachment {
  id: number;
  name: string;
  description: string;
}

interface PaymentLink {
  id: number;
  recipientName: string;
  recipientEmail: string;
  createdAt: string;
  amount: number;
  note: string;
  status: string;
  paidOn: string | null;
  attachments?: Attachment[];
}

interface Column {
  id: string;
  label: string;
  width?: string;
}

// Mock data
const mockPaymentLinks: PaymentLink[] = [
  {
    id: 1,
    recipientName: 'Abrahma Sabel',
    recipientEmail: '<EMAIL>',
    createdAt: '2025-05-01',
    amount: 135,
    note: 'Client Invoice Attachment.pdf',
    status: 'Pending',
    paidOn: '2025-05-02',
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
      { id: 3, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
  },
  {
    id: 2,
    recipientName: 'Abrahma Sabel',
    recipientEmail: '<EMAIL>',
    createdAt: '2025-05-01',
    amount: 135,
    note: 'Client Invoice Attachment.pdf',
    status: 'Paid',
    paidOn: '2025-05-02',
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
  },
  {
    id: 3,
    recipientName: 'Abrahma Sabel',
    recipientEmail: '<EMAIL>',
    createdAt: '2025-05-01',
    amount: 135,
    note: 'Client Invoice Attachment.pdf',
    status: 'Cancelled',
    paidOn: '2025-05-02',
  },
  {
    id: 4,
    recipientName: 'Abrahma Sabel',
    recipientEmail: '<EMAIL>',
    createdAt: '2025-05-01',
    amount: 135,
    note: 'Client Invoice Attachment.pdf',
    status: 'Expired',
    paidOn: '2025-05-02',
    attachments: [
      { id: 1, name: 'Attachment.Doc', description: 'JaneDoe456 Contract.docx' },
      { id: 2, name: 'Attachment.pdf', description: 'Client Invoice Attachment.pdf' },
    ],
  },
];

export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Paid':
      return '#058205';
    case 'Pending':
      return '#204464';
    case 'Cancelled':
      return '#F03D3E';
    case 'Expired':
      return '#EF6C00';
    default:
      return '#000000';
  }
};

const PaymentLinks: React.FC = () => {
  // Use proper typing for the anchor element state
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [filterList, setFilterList] = useState({
    multiFilter: { "Status": ["Paid"], "Recipient Name": ["Hello"] },
  });

  const [page, setPage] = useState(1);
  const [paymentLinks] = useState<PaymentLink[]>(mockPaymentLinks);
  const [selectedAttachments, setSelectedAttachments] = useState<Attachment[]>([]);
  const [attachmentAnchorEL, setAttachmentAnchorEL] = useState<HTMLElement | null>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState(false);
  const [openCancelAlert, setOpenCancelAlert] = useState(false);

  const [openCreatePaymentLinkModal, setOpenCreatePaymentLinkModal] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, data: PaymentLink) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(paymentLinks.find((item) => item.id === data.id) || null);
  };

  const handleAttachments = () => {
    setAnchorEl(null);
    setOpenAttachmentsModal(true);
  }

  const handleAttachmentSave = (files: any[]) => {
    console.log("files", files)
  }

  const renderHeaderCell = (column: Column) => {
    return column.label;
  }

  const renderCell = (row: PaymentLink, column: Column) => {
    switch (column.id) {
      case 'recipientName':
        return (
          <Box sx={{textAlign: 'left'}}>
            <ListItemText primary={row.recipientName} />
            <Typography sx={{ color: '#204464', textDecoration: 'underline', fontSize: '12px' }}>
              {row.recipientEmail}
            </Typography>
          </Box>
        );
      case 'status':
        return (
          <CustomButton
            size="small"
            sx={{
              width: '110px',
              color: getStatusColor(row.status),
              backgroundColor: `${getStatusColor(row.status)}0D`,
              border: `1px solid ${getStatusColor(row.status)}`,
              '&:hover': {
                backgroundColor: `${getStatusColor(row.status)}1A`,
                border: `1px solid ${getStatusColor(row.status)}`
              },
              '& .MuiBox-root': {
                display: 'flex',
                alignItems: 'center',
                gap: '5px'
              }
            }}
          >
            {row.status}
          </CustomButton>
        );
      case "attachment":
        return row.attachments?.length ? (
          <AttachFileIcon
            onMouseEnter={(event: any) => {
              setAttachmentAnchorEL(event.currentTarget);
              setSelectedAttachments(row.attachments || []);
            }}
            onMouseLeave={() => {
              setAttachmentAnchorEL(null);
            }}
            sx={{ width: '16px', height: '16px', rotate: '45deg', color: '#00000099' }}
          />
        ) : null;
      case "actions":
        return (
          <IconButton
            sx={styles.moreButton}
            onClick={(e) => handleMenuOpen(e, row)}
          >
            <MoreVerticalIcon />
          </IconButton>
        );
      case "amount":
        return `$${row.amount}`
      default:
        return row[column.id as keyof PaymentLink];
    }
  };

  return (
    <Box sx={styles.wrapper}>
      <Box sx={styles.titleContainer} >
        <Box>
          <Typography sx={styles.title}>
            My Payment Links
          </Typography>

          <Typography
            variant="body1"
            color="text.secondary"
            sx={styles.description}
          >
            Below is the list of all your Payment links
          </Typography>
        </Box>
        <Box>
          <HelpOutlineIcon sx={styles.helpIcon} />
        </Box>
      </Box>

      <Box sx={styles.actionContainer}>
        <CustomButton
          variant="outlined"
          endIcon={<FilterIcon color="#1a3850" />}
          color="secondary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => setFilterMenuAnchorEl(e.currentTarget)}
        >
          Filter
        </CustomButton>

        <CustomButton
          variant="outlined"
          startIcon={<AddIcon sx={{ fontSize: '20px' }} />}
          color="primary"
          onClick={() => {
            setOpenCreatePaymentLinkModal(true);
          }}
        >
          New
        </CustomButton>
      </Box>

      <CustomTable
        columns={[
          { id: 'recipientName', label: 'Recipient Name' },
          { id: 'createdAt', label: 'Data Created', width: '120px' },
          { id: 'amount', label: 'Amount', width: '100px' },
          { id: 'note', label: 'Note'},
          { id: 'status', label: 'Status', width: '140px' },
          { id: 'paidOn', label: 'Paid On', width: '140px' },
          { id: 'attachment', label: '', width: '50px' },
          { id: 'actions', label: 'Actions', width: '80px' },
        ]}
        data={paymentLinks}
        renderCell={renderCell}
        renderHeaderCell={renderHeaderCell}
        isCenteredCells={true}
      />

      <Box sx={styles.paginationContainer}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                previous: () => 'Previous',
                next: () => 'Next',
              }}
              {...item}
              sx={styles.paginationItem}
            />
          )}
          sx={styles.pagination}
        />
      </Box>


      <FilterMenu
        anchorEl={filterMenuAnchorEl}
        setAnchorEl={setFilterMenuAnchorEl}
        setFilterList={setFilterList}
        filterList={filterList}
      />

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        PaperProps={{
          sx: styles.menuPaper,
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={() => { setOpenCancelAlert(true); setAnchorEl(null); }} sx={styles.menuItem}>
          <PaymentCancelIcon color = "#204464" />
          Cancel
        </MenuItem>
        <MenuItem onClick={() => { setAnchorEl(null); }} sx={styles.menuItem}>
          <PaymentResendIcon color = "#204464" />
          Resend
        </MenuItem>
        <MenuItem onClick={() => { setAnchorEl(null); }} sx={styles.menuItem}>
          <PaymentCopyLinkIcon color = "#204464" />
          Copy Link
        </MenuItem>
        <MenuItem onClick={handleAttachments} sx={styles.menuItem}>
          <LinkIcon sx={{ rotate: '-45deg', width: '18px', height: '18px' }} />
          Attachments
        </MenuItem>
        <MenuItem onClick={() => { setAnchorEl(null); }} sx={styles.menuItem}>
          <PaymentDownloadIcon color = "#204464" />
          Download Recipient
        </MenuItem>
      </Menu>

      <Popper
        open={Boolean(attachmentAnchorEL)}
        anchorEl={attachmentAnchorEL}
        placement="bottom-end"
        style={{ zIndex: 9999 }}
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 8],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={200}>
            <Paper sx={{ ...styles.tagTablePaper, minWidth: '230px' }}>
              {selectedAttachments?.map((attachment: any, index) => (
                <AttachmentItem attachment={attachment} key={`table_attachment_${index}`} />
              ))}
            </Paper>
          </Fade>
        )}
      </Popper>

      <CreatePaymentLinkModal 
        open = {openCreatePaymentLinkModal}
        onClose = {() => setOpenCreatePaymentLinkModal(false)}
      />

      <CancelPaymentLinkAlert
        open={openCancelAlert}
        onClose={() => setOpenCancelAlert(false)}
        onConfirm={() => {
          setOpenCancelAlert(false);
        }}
      />

      <AttachmentsModal
        open={openAttachmentsModal}
        onClose={() => setOpenAttachmentsModal(false)}
        title="View Attachments"
        handleSave={handleAttachmentSave}
        attachments={selectedItem?.attachments}
      />

    </Box>
  )
}

export default PaymentLinks;
