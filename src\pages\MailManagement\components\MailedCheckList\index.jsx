import { useState } from "react";
import { CustomTable } from "../../../../components/table/CustomTable";
import { Box } from "@mui/material";
import { BatchDetailDialog } from "../modals/BatchDetailModal/index.jsx";

const mockChecks = [
  {
    id: 1,
    no: 1,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "ABC Corp",
    issuedDate: "2/12/2025",
    batchNo: "B001",
    mailedDate: "2/12/2025",
  },
  {
    id: 2,
    no: 2,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "ABC Corp",
    issuedDate: "2/12/2025",
    batchNo: "B002",
    mailedDate: "2/12/2025",
  },
  {
    id: 3,
    no: 3,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "-",
    issuedDate: "2/12/2025",
    batchNo: "B003",
    mailedDate: "2/12/2025",
  },
  {
    id: 4,
    no: 4,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "-",
    issuedDate: "2/12/2025",
    batchNo: "B001",
    mailedDate: "2/12/2025",
  },
  {
    id: 5,
    no: 5,
    payeeName: "<PERSON>",
    account: "JohnDoe123",
    org: "XYZ Inc",
    issuedDate: "2/12/2025",
    batchNo: "B001",
    mailedDate: "2/12/2025",
  },
];
// { id: "no", label: "Batch No.", width: "180px" },
//           { id: "createdBy", label: "Created By", width: "220px" },
//           { id: "createdDate", label: "Created Date", width: "140px" },
//           { id: "total", label: "Total Checks", width: "200px" },
//           { id: "processing", label: "Processing", width: "140px" },
//           { id: "mailed", label: "Mailed", width: "120px" },
const mockSelectedBatch = {
  createdDate: '01/12/2025',
  createdBy: 'Admin01',
  total: '10',
  processing: '6',
  mailed: '4'
}

export const MailedCheckList = () => {
  const [checks,] = useState(mockChecks);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);

  const handleBatchDetailClick = (batchNo) => {
    // setSelectedBatch(null);
    if (!batchNo || batchNo === null || batchNo === undefined) return;

    setSelectedBatch({
      ...mockSelectedBatch,
      no: batchNo,
    });
    setOpenDialog(true);
  }

  const handleCloseDialog = () => {
    setOpenDialog(false);
  }

  const renderCell = (row, column) => {
    switch (column.id) {
      case "batchNo":
        return (
          <Box sx={{ textDecoration: 'underline', cursor: 'pointer' }} onClick={() => handleBatchDetailClick(row.batchNo)}>
            {row.batchNo}
          </Box>
        );
      default:
        return row[column.id];
    }
  };

  return (
    <>
      <CustomTable
        columns={[
          { id: "selected", label: "", width: "40px" },
          { id: "no", label: "Check No.", width: "40px" },
          { id: "payeeName", label: "Payee Name", width: "220px" },
          { id: "account", label: "User Account", width: "140px" },
          { id: "org", label: "Organization", width: "200px" },
          { id: "issuedDate", label: "Issued Date", width: "140px" },
          { id: "mailedDate", label: "Mailed Date", width: "140px" },
          { id: "batchNo", label: "Batch No.", width: "120px" },
        ]}
        data={checks}
        isCenteredCells={true}
        renderCell={renderCell}
      />
      <BatchDetailDialog
        open={openDialog}
        onClose={handleCloseDialog}
        batch={selectedBatch}
      />
    </>
  );
};
