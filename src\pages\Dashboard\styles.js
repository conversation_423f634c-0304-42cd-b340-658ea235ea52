export const styles = {
  wrapper: {
    p: { xs: 1, sm: 3 },
    width: '100%'
  },
  titleContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    mb: '24px',
    py: 2,
    borderBottom: '1px solid #E0E0E0'
  },
  title: {
    fontSize: {
      xs: '20px',
      sm: '32px'
    },
    lineHeight: '100%',
    fontWeight: 600,
  },
  selectContainer: {
    display: 'flex',
    gap: 1,
    alignItems: 'center'
  },
  selectProfile: {
    '& .MuiSelect-select': {
      minWidth: {sm: '180px'},
      minHeight: { sm: '40px', xs: '32px' },
      backgroundColor: '#FFF',
      borderRadius: '4px',
      border: '1px solid #E2E6E9',
      padding: {sm: '0 12px', xs: '0 8px'},
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      fontSize: { sm: '14px', xs: '10px' },
      color: '#00000099'
    },
    '& .MuiSvgIcon-root': {
      color: '#666'
    }
  },
  selectType: {
    '& .MuiSelect-select': {
      minHeight: { sm: '40px', xs: '32px' },
      backgroundColor: '#F9FAFB',
      borderRadius: '4px',
      border: '1px solid #E2E6E9',
      padding: {sm: '0 12px', xs: '0 8px'},
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      fontSize: { sm: '14px', xs: '10px' },
      color: '#00000099'
    },
    '& .MuiSvgIcon-root': {
      color: '#666'
    }
  },
  statisticsContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', sm: 'row' },
    gap: { xs: '12px', sm: '24px' },
    mb: { xs: '12px', sm: '24px' }
  },
  cardTotalContainer: {
    backgroundColor: '#292D32',
    borderRadius: '6px',
    padding: { xs: '16px', sm: '24px' },
    width: { sm: '33.3%', xs: '100%' }
  },
  cardClearedContainer: {
    backgroundColor: '#EBFBE8',
    borderRadius: '6px',
    padding: { xs: '16px', sm: '24px' },
    width: { sm: '33.3%', xs: '100%' }
  },
  cardDraftContainer: {
    backgroundColor: '#F1F4FF',
    borderRadius: '6px',
    padding: { xs: '16px', sm: '24px' },
    width: { sm: '33.3%', xs: '100%' }
  },
  cardHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    mb: { xs: '12px', sm: '24px' }
  },
  cardTotalTitle: {
    fontSize: { sm: '16px', xs: '12px' },
    color: '#E2E6E9'
  },
  cardClearedTitle: {
    fontSize: { sm: '16px', xs: '12px' },
    color: '#000000DE'
  },
  cardDraftTitle: {
    fontSize: { sm: '16px', xs: '12px' },
    color: '#000000DE'
  },
  cardTotalIcon: {
    color: '#E2E6E9',
    fontSize: { sm: '20px', xs: '16px' },
  },
  cardClearedIcon: {
    color: '#00000099',
    fontSize: { sm: '20px', xs: '16px' },
  },
  cardDraftIcon: {
    color: '#00000099',
    fontSize: { sm: '20px', xs: '16px' },
  },
  cardTotalAmount: {
    fontSize: { sm: '32px', xs: '24px' },
    color: '#FFFFFF',
    fontWeight: 600,
  },
  cardClearedAmount: {
    fontSize: { sm: '32px', xs: '24px' },
    color: '#000000DE',
    fontWeight: 600,
  },
  cardDraftAmount: {
    fontSize: '32px',
    color: '#000000DE',
    fontWeight: 600,
  },
  chartContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: { xs: 'column', sm: 'row' },
    gap: { xs: '12px', sm: '24px' }
  },
  chartBox: {
    width: { sm: '50%', xs: '100%' },
    borderRadius: '12px',
    border: '1px solid #E2E6E9',
    padding: { xs: '16px', sm: '24px' },
    mb: 'auto'
  },
  chartHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    mb: { xs: '8px', sm: '12px' }
  },
  chartTitle: {
    fontSize: { sm: '24px', xs: '20px' },
    color: '#000000DE',
    fontWeight: 600
  },
  chartDate: {
    fontSize: { sm: '14px', xs: '12px' },
    color: '#00000099',
    fontWeight: 400
  },
  chartSelect: {
    '& .MuiSelect-select': {
      height: { sm: '32px', xs: '32px' },
      width: { sm: '80px', xs: '40px' },
      backgroundColor: '#F9FAFB',
      borderRadius: '4px',
      border: '1px solid #E2E6E9',
      padding: '0 12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      fontSize: { sm: '14px', xs: '12px' },
      color: '#00000099'
    },
    '& .MuiSvgIcon-root': {
      color: '#666'
    }
  },
  viewAllButton: {
    fontSize: '12px',
    lineHeight: '20px',
    height: '40px',
    minWidth: '100px',
    color: '#058205',
    border: 'none',
    textTransform: 'none',
    '&:hover': {
      border: 'none',
      backgroundColor: 'rgba(0, 0, 0, 0.04)'
    }
  },
}