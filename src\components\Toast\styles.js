export const styles = {
  snackbar: {
    mt: 8,
  },
  toast: {
    fontSize: "20px",
    width: "100%",
    py: "16px",
    px: "24px",
    minHeight: "66px",
    '& .<PERSON>i<PERSON>lert-icon': {
      display: 'none',
    },
    '& .<PERSON>i<PERSON>lert-message': {
      p: 0,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
    },
  },
  successToast: {
    backgroundColor: "#05820526",
    color: "#058205",

    '& .MuiAlert-action': { 
      color: "#058205",
      p: "0px 0px 0px 16px",
    },
  },
  errorToast: {
    backgroundColor: "#FFEBEE",
    color: "#C62828",

    '& .MuiAlert-action': {
      color: "#C62828",
      p: "0px 0px 0px 16px",
    },
  }
};
