import React, { useRef, useState } from "react";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { Box, Typography, Select, MenuItem, TextField, Checkbox, ListItemText, SelectChangeEvent } from "@mui/material";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { Formik, FormikProps } from "formik";
import * as Yup from "yup";
import { styles } from "../../../styles";
import { DocumentIcon } from '../../../../../components/Icons';
import AttachFileOutlinedIcon from '@mui/icons-material/AttachFileOutlined';
import { GeneratePaymentLinkAlert } from '../GeneratePaymentLinkAlert';
import { AttachmentsModal } from "../AttachmentsModal";

interface Attachment {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  file: File;
  description?: string;
}

interface FormValues {
  recipientEmail: string;
  recipientName: string;
  amount: string;
  note: string;
  paidOn: string;
  purpose: string;
  attachments: Attachment[];
}

interface CreatePaymentLinkModalProps {
  open: boolean;
  onClose: () => void;
}

interface FileData {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  file?: File;
  description?: string;
}

export const CreatePaymentLinkModal: React.FC<CreatePaymentLinkModalProps> = ({ open, onClose }) => {
  const formRef = useRef<FormikProps<FormValues>>(null);
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [openPaymentLink, setOpenPaymentLink] = useState<boolean>(false);
  const [paymentLink, setPaymentLink] = useState<string>("https://pay.example.com/payment/8f3d9a...");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      processFiles(selectedFiles);
      // Reset the file input value so the same file can be selected again
      e.target.value = '';
    }
  };

  const processFiles = (newFiles: File[]): void => {
    const processedFiles = newFiles.map(file => {
      // Create URL for preview
      const url = URL.createObjectURL(file);
      return {
        name: file.name,
        size: file.size,
        type: file.type,
        url,
        file,
        id: Date.now() + Math.random().toString(36).substr(2, 9) // Add unique ID
      };
    });

    if (formRef.current) {
      const currentAttachments = formRef.current.values.attachments || [];
      formRef.current.setFieldValue('attachments', [...currentAttachments, ...processedFiles]);
    }
  };

  const initialValues: FormValues = {
    recipientEmail: '',
    recipientName: '',
    amount: '',
    note: '',
    paidOn: '',
    purpose: '',
    attachments: [],
  };

  const validationSchema = Yup.object().shape({
    recipientEmail: Yup.string().required("Recipient Email is required"),
    recipientName: Yup.string().required("Recipient Name is required"),
    amount: Yup.string().required("Amount is required"),
    note: Yup.string().required("Note is required"),
    paidOn: Yup.string().required("Due Date is required"),
    purpose: Yup.string().required("Purpose is required"),
  });

  const handleCreateLink = (): void => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  };

  const handleSubmit = async (values: FormValues, { setSubmitting, resetForm }: { setSubmitting: (isSubmitting: boolean) => void, resetForm: () => void }): Promise<void> => {
    try {
      onClose();
      console.log('Updating check:', values);
      resetForm();
    } catch (error) {
      console.error('Error submitting check:', error);
    } finally {
      setOpenPaymentLink(true);
      setPaymentLink("https://pay.example.com/payment/8f3d9a...");
      setSubmitting(false);
    }
  };

  const handleSaveAttachments = (attachments: FileData[]): void => {
    if (formRef.current) {
      formRef.current.setFieldValue('attachments', attachments);
    }
  };

  return (
    <>
      <CustomDialog
        open={open}
        onClose={onClose}
        width="760px"
        title="Create Payment Link"
        content={
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            innerRef={formRef}
          >
            {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
              <Box>
                <Typography color="#00000099" sx={{ mb: "24px" }}>
                  Easily generate a secure payment request and share it with your customer via email.
                </Typography>
                <Box sx={styles.formContainer}>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Recipient Email*</Typography>

                    <Select
                      fullWidth
                      name="recipientEmail"
                      value={values.recipientEmail}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return (
                            <Typography sx={{ color: "text.secondary" }}>
                              Recipient Email
                            </Typography>
                          );
                        }
                        return selected;
                      }}
                      error={touched.recipientEmail && !!errors.recipientEmail}
                      sx={styles.select}
                    >
                      <MenuItem value="<EMAIL>">
                        <Checkbox
                        />
                        <Box>
                          <ListItemText primary={'Abraham Sabel'} />
                          <Typography sx={{ color: '#00000099', textDecoration: 'underline', fontSize: '12px' }}>
                            {'<EMAIL>'}
                          </Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value="<EMAIL>">
                        <Checkbox
                        />
                        <Box>
                          <ListItemText primary={'Sabel'} />
                          <Typography sx={{ color: '#00000099', textDecoration: 'underline', fontSize: '12px' }}>
                            {'<EMAIL>'}
                          </Typography>
                        </Box>
                      </MenuItem>
                    </Select>
                    {touched.recipientEmail && errors.recipientEmail && (
                      <Typography color="error" sx={{ ...styles.errorText, mt: '-3px' }}>
                        {errors.recipientEmail}
                      </Typography>
                    )}

                  </Box>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Recipient Name*</Typography>
                    <TextField
                      fullWidth
                      name="recipientName"
                      placeholder="Recipient Name"
                      value={values.recipientName}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.recipientName && !!errors.recipientName}
                      helperText={touched.recipientName && errors.recipientName}
                      sx={{
                        ...styles.input,
                        width: '100% !important'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={styles.formContainer}>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Amount*</Typography>
                    <TextField
                      fullWidth
                      name="amount"
                      placeholder="Enter amount"
                      value={values.amount}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.amount && !!errors.amount}
                      helperText={touched.amount && errors.amount}
                      sx={{
                        ...styles.input,
                        width: '100% !important'
                      }}
                    />
                  </Box>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Note</Typography>
                    <TextField
                      fullWidth
                      name="note"
                      placeholder="Enter Note"
                      value={values.note}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.note && !!errors.note}
                      helperText={touched.note && errors.note}
                      sx={{
                        ...styles.input,
                        width: '100% !important'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={styles.formContainer}>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Due Date</Typography>
                    <TextField
                      fullWidth
                      name="paidOn"
                      placeholder="Due Date"
                      value={values.paidOn}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.paidOn && !!errors.paidOn}
                      helperText={touched.paidOn && errors.paidOn}
                      sx={{
                        ...styles.input,
                        width: '100% !important'
                      }}
                    />
                  </Box>
                  <Box sx={styles.formItemContainer}>
                    <Typography sx={styles.formItemLabel}>Purpose</Typography>
                    <TextField
                      fullWidth
                      name="purpose"
                      placeholder="Purpose"
                      value={values.purpose}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.purpose && !!errors.purpose}
                      helperText={touched.purpose && errors.purpose}
                      sx={{
                        ...styles.input,
                        width: '100% !important'
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={{ display: 'flex' }}>
                  <Box
                    sx={{
                      ...styles.addAttachmentButton,
                      borderColor: values.attachments.length > 0 ? '#204464' : '#00000099',
                      borderStyle: isDragging ? 'dashed' : 'dashed',
                      borderWidth: isDragging ? '2px' : '1px',
                      backgroundColor: isDragging ? '#F0F9FF' : 'transparent',
                    }}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => setOpenAttachmentsModal(true)}
                  >
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileInput}
                      style={{ display: 'none' }}
                      multiple
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      <DocumentIcon color={values.attachments.length > 0 ? '#204464' : '#00000099'} />
                      <Typography sx={{ fontSize: '18px', color: values.attachments.length > 0 ? '#204464' : '#00000099', lineHeight: 'normal' }}>
                        {(values.attachments && values.attachments.length > 0)
                          ? `${values.attachments.length} ${values.attachments.length === 1 ? 'file' : 'files'} attached`
                          : 'Add Attachments'}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            )}
          </Formik>
        }
        actions={
          <>
            <CustomButton
              variant="outlined"
              onClick={onClose}
              sx={{ minWidth: '100px' }}
            >
              Cancel
            </CustomButton>
            <CustomButton
              variant="contained"
              color="primary"
              onClick={handleCreateLink}
              sx={{ minWidth: '100px' }}
            >
              Create Link
            </CustomButton>
          </>
        }
      />

      {openAttachmentsModal && (
        <AttachmentsModal
          open={openAttachmentsModal}
          onClose={() => setOpenAttachmentsModal(false)}
          attachments={formRef.current?.values.attachments || []}
          handleSave={handleSaveAttachments}
        />
      )}

      <GeneratePaymentLinkAlert
        open={openPaymentLink}
        onClose={() => setOpenPaymentLink(false)}
        onConfirm={() => setOpenPaymentLink(false)}
        link={paymentLink}
      />
    </>
  );
};

export default CreatePaymentLinkModal;
