import React from "react";
import {
  <PERSON>,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
} from "@mui/material";
import { GeneratingCheckIcon, PDFIcon } from "../../../../../components/Icons";
import { styles } from "../../../styles";

export const GeneratingCheckModal = ({ open }) => {
  return (
    <Dialog
      open={open}
      // onClose={onClose}
      PaperProps={{
        sx: styles.dialog,
      }}
    >
      <Box sx={styles.dialogHeader}>
        <DialogTitle sx={styles.dialogTitle}></DialogTitle>
        {/* <IconButton onClick={onClose} sx={styles.dialogCloseButton}>
          <CloseIcon />
        </IconButton> */}
      </Box>
      <DialogContent sx={{ p: 0 , mb: "50px" }}>
        <Box sx={styles.generatingCheckIconContainer}>
          <GeneratingCheckIcon />
          <Box sx={styles.pdfIconContainer}>
            <PDFIcon />
          </Box>
        </Box>
        <Typography
          sx={{ ...styles.dialogTitle, xs: { mb: "19px" }, mb: "30px" }}
          align="center"
        >
          Generating Check...
        </Typography>
        <Typography sx={styles.dialogContent} color="text.secondary" align="center">
          Please wait while we generate your check. This may take a few seconds.
        </Typography>
      </DialogContent>
    </Dialog>
  );
};
