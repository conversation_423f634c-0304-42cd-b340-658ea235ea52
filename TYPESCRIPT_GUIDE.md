# TypeScript Setup Guide for Check Writer Prototype

This guide explains how to use TypeScript in your Check Writer prototype application.

## 🎉 What's Been Set Up

Your project now has complete TypeScript support with:

- ✅ TypeScript compiler configuration (`tsconfig.json`)
- ✅ Type definitions for React and major dependencies
- ✅ ESLint configuration with TypeScript rules
- ✅ Common type definitions (`src/types/`)
- ✅ TypeScript utility functions and hooks
- ✅ Example TypeScript components
- ✅ Path aliases for cleaner imports

## 📁 Project Structure

```
src/
├── types/
│   ├── index.ts          # Common application types
│   └── react.ts          # React-specific types
├── common/
│   ├── hooks.ts          # TypeScript custom hooks
│   └── utils.ts          # TypeScript utility functions
├── components/
│   └── examples/
│       └── TypeScriptButton.tsx  # Example TS component
└── ... (your existing files)
```

## 🚀 Getting Started

### 1. Install Dependencies

Since you may have PowerShell execution policy restrictions, you can install the TypeScript dependencies manually by running this in an elevated PowerShell or Command Prompt:

```bash
npm install --save-dev @types/node @types/jest @types/js-cookie @types/uuid @types/react-router-dom @types/react-color @types/react-signature-canvas @typescript-eslint/eslint-plugin @typescript-eslint/parser
```

### 2. Type Checking

Run TypeScript type checking:

```bash
npm run type-check          # One-time check
npm run type-check:watch    # Watch mode
```

### 3. Creating New TypeScript Components

When creating new components, use `.tsx` extension and follow this pattern:

```typescript
import React from 'react';
import { ComponentProps } from '@/types/react';

interface MyComponentProps extends ComponentProps {
  title: string;
  count?: number;
  onAction: (id: string) => void;
}

const MyComponent: React.FC<MyComponentProps> = ({
  title,
  count = 0,
  onAction,
  className,
  children,
}) => {
  return (
    <div className={className}>
      <h2>{title}</h2>
      <p>Count: {count}</p>
      {children}
    </div>
  );
};

export default MyComponent;
```

## 📝 Converting Existing Components

To convert your existing `.jsx` files to TypeScript:

1. **Rename the file** from `.jsx` to `.tsx`
2. **Add type annotations** for props:

```typescript
// Before (JSX)
const MyComponent = ({ title, onSave }) => {
  // component code
};

// After (TypeScript)
interface Props {
  title: string;
  onSave: (data: any) => void;
}

const MyComponent: React.FC<Props> = ({ title, onSave }) => {
  // component code
};
```

3. **Add types for state and variables**:

```typescript
// Before
const [user, setUser] = useState(null);
const [loading, setLoading] = useState(false);

// After
const [user, setUser] = useState<User | null>(null);
const [loading, setLoading] = useState<boolean>(false);
```

## 🔧 Available Types

### Common Application Types

Import from `@/types`:

```typescript
import { User, Check, BankAccount, ApiResponse } from '@/types';
```

### React Component Types

Import from `@/types/react`:

```typescript
import { BaseComponentProps, ModalProps, FormProps } from '@/types/react';
```

## 🪝 Custom Hooks

Use the provided TypeScript hooks:

```typescript
import { useLoading, useForm, useApi } from '@/common/hooks';

// Loading state
const { loading, startLoading, stopLoading } = useLoading();

// Form management
const { values, errors, setValue, validate } = useForm({
  name: '',
  email: '',
});

// API calls
const { data, loading, error, execute } = useApi(fetchUserData);
```

## 🛠 Utility Functions

Use type-safe utilities:

```typescript
import { formatCurrency, formatDate, validators } from '@/common/utils';

// Format currency
const price = formatCurrency(1234.56); // "$1,234.56"

// Validate email
const isValid = validators.email('<EMAIL>'); // true

// Type-safe grouping
const grouped = groupBy(users, 'role'); // Groups users by role
```

## 📦 Path Aliases

Use clean imports with path aliases:

```typescript
// Instead of
import { User } from '../../../types';
import Button from '../../../components/Button';

// Use
import { User } from '@/types';
import Button from '@/components/Button';
```

## 🎯 Best Practices

### 1. Always Type Your Props

```typescript
interface Props {
  title: string;
  optional?: boolean;
  callback: (id: string) => void;
}
```

### 2. Use Union Types for Variants

```typescript
type ButtonVariant = 'primary' | 'secondary' | 'danger';
type Status = 'loading' | 'success' | 'error';
```

### 3. Type Your API Responses

```typescript
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

const fetchUsers = (): Promise<ApiResponse<User[]>> => {
  // API call
};
```

### 4. Use Type Guards

```typescript
const isUser = (obj: any): obj is User => {
  return obj && typeof obj.id === 'string' && typeof obj.email === 'string';
};
```

## 🔍 IDE Support

### VS Code Extensions

Install these extensions for the best TypeScript experience:

- TypeScript Importer
- Auto Rename Tag
- Bracket Pair Colorizer
- ES7+ React/Redux/React-Native snippets

### IntelliSense

Your IDE will now provide:
- ✅ Auto-completion
- ✅ Type checking
- ✅ Refactoring support
- ✅ Import suggestions
- ✅ Error highlighting

## 🐛 Common Issues

### 1. Module Not Found

If you get module not found errors, make sure your `tsconfig.json` includes the correct paths.

### 2. Type Errors

Start with `any` type and gradually add more specific types:

```typescript
// Start with
const data: any = fetchData();

// Gradually improve to
const data: User[] = fetchData();
```

### 3. Missing Type Definitions

If a library doesn't have types, create a declaration file:

```typescript
// types/custom.d.ts
declare module 'some-library' {
  export function someFunction(): void;
}
```

## 📚 Next Steps

1. **Start converting components** one by one from `.jsx` to `.tsx`
2. **Add types gradually** - don't try to type everything at once
3. **Use the example components** as reference
4. **Run type checking regularly** to catch errors early
5. **Leverage IDE features** for better development experience

## 🆘 Getting Help

- Check the example components in `src/components/examples/`
- Review the type definitions in `src/types/`
- Use the TypeScript handbook: https://www.typescriptlang.org/docs/
- React TypeScript cheatsheet: https://react-typescript-cheatsheet.netlify.app/

Happy coding with TypeScript! 🎉
