import { useRef, useState } from "react";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { Box, Typography, Button, Select, MenuItem, TextField, Radio, InputAdornment, Checkbox, Divider } from "@mui/material";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { Formik } from "formik";
import * as Yup from "yup";
import { styles } from "../../../styles";
import { numberToCurrencyWords, sleep, formatAmountAsCurrency } from "../../../../../common/utils";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { TagIcon } from "../../../../../components/Icons";
import { DocumentIcon } from '../../../../../components/Icons';
import AttachFileOutlinedIcon from '@mui/icons-material/AttachFileOutlined';
import { AttachmentsModal } from "../AttachmentsModal";

const otherTags = [
  { color: '#058205', value: 'Business' },
  { color: '#EF6C00', value: 'Personal' },
  { color: '#D2BA00', value: 'Miscellaneous' },
]
const myTags = [
  { color: '#F03D3E', value: 'Tag1' },
  { color: '#3EA5F9', value: 'Tag2' },
  { color: '#00CF72', value: 'Tag3' },
]

export const EditModal = ({ open, onClose, handleOpenUnsavedAlert, checkData, handleOpenDownloadCheckAlert, setOpenGeneratingCheckDialog }) => {
  const formRef = useRef(null);
  const isblank = checkData?.status === 'Blank'
  const [isPrinting, setIsPrinting] = useState(false);
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  };

  const handleFileInput = (e) => {
    const selectedFiles = Array.from(e.target.files);
    if (selectedFiles.length > 0) {
      processFiles(selectedFiles);
      // Reset the file input value so the same file can be selected again
      e.target.value = '';
    }
  };

  const processFiles = (newFiles) => {
    const processedFiles = newFiles.map(file => {
      // Create URL for preview
      const url = URL.createObjectURL(file);
      return {
        name: file.name,
        size: file.size,
        type: file.type,
        url: url,
        file: file,
        id: Date.now() + Math.random().toString(36).substr(2, 9) // Add unique ID
      };
    });

    if (formRef.current) {
      const currentAttachments = formRef.current.values.attachments || [];
      formRef.current.setFieldValue('attachments', [...currentAttachments, ...processedFiles]);
    }
  };

  const initialValues = {
    bankAccount: checkData?.bankAccount || "",
    payeeName: checkData?.payeeName || "",
    checkNumber: checkData?.checkNumber || "2051",
    issuedDate: checkData?.issuedDate || new Date().toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
    amount: checkData?.amount || "",
    memo: checkData?.memo || "",
    dollarAmount: checkData?.amount ? numberToCurrencyWords(Number(checkData?.amount)) : "",
    signature: checkData?.signature || true,
    invoiceId: checkData?.invoiceId || "",
    tags: checkData?.tags || [],
    attachments: checkData?.attachments || [],
  }
  const validationSchema = Yup.object().shape({
    bankAccount: Yup.string().required("Bank Account is required"),
    payeeName: Yup.string().required("Payee Name is required"),
    checkNumber: Yup.string().required("Check Number is required"),
    issuedDate: Yup.string().required("Issued Date is required"),
    amount: Yup.string().required("Amount is required"),
    dollarAmount: Yup.string().required("Dollar Amount is required"),
    signature: Yup.boolean().required("Signature is required"),
    invoiceId: Yup.string().required("Invoice Id is required"),
  })
  const handleSaveAndPrint = () => {
    setIsPrinting(true);
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }
  const handleSaveCheck = () => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  }

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    try {
      onClose();
      console.log('Updating check:', values);
      if (isPrinting) {
        setOpenGeneratingCheckDialog(true);
        await sleep(2000);
      }
      resetForm();
    } catch (error) {
      console.error('Error submitting check:', error);
    } finally {
      if (isPrinting) {
        handleOpenDownloadCheckAlert();
      }
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    console.log("checkData", checkData)
    if (checkData && formRef.current) {
      const formValues = formRef.current.values;
      const hasChanges = Object.keys(formValues).some(key => {
        if (key === 'memo' || key === 'dollarAmount') {
          return false;
        }
        if (key === 'tags')
          return formValues['tags'].join(',') !== checkData['tags'].join(',')
        if (key === 'attachments') {
          // Compare attachments by length for simplicity
          return formValues['attachments'].length !== (checkData['attachments'] || []).length;
        }
        return formValues[key] !== checkData[key];
      });

      if (hasChanges) {
        handleOpenUnsavedAlert();
      }
      else {
        onClose();
      }
    }
  };

  const handleSaveAttachments = (attachments) => {
    if (formRef.current) {
      formRef.current.setFieldValue('attachments', attachments);
    }
  };

  return (
    <>
      <CustomDialog
        open={open}
        onClose={handleClose}
        width="1142px"
        title="Edit Check"
        content={
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            innerRef={formRef}
          >
            {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
              <Box>
                <Typography color="#00000099" sx={{ mb: "24px" }}>
                  Edit your check information for accurate printing and transactions.
                </Typography>
                <Box sx={styles.formContainer}>
                  <Box sx={styles.addCheckFormContainer}>
                    <Box sx={styles.addFormHeader}>
                      <Box>
                        <Box sx={styles.titleContainer}>
                          <Box>
                            <Typography sx={styles.checkTitle}>
                              Haseeb Iqbal
                            </Typography>
                            <Typography sx={styles.checkSubTitle}>
                              Address Line 1, Address Line 2 <br />
                              ashkasham, Badakhshan, 23123
                            </Typography>
                          </Box>
                          <Box sx={styles.itemContainer}>
                            <Typography sx={styles.itemLabel}>
                              Bank <br /> Account *
                            </Typography>
                            <Box sx={{ opacity: isblank ? 0.5 : 1 }}>
                              <Select
                                fullWidth
                                name="bankAccount"
                                value={values.bankAccount}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                displayEmpty
                                disabled={isblank}
                                renderValue={(selected) => {
                                  if (!selected) {
                                    return (
                                      <Typography sx={{ color: "text.secondary" }}>
                                        Select Bank Account
                                      </Typography>
                                    );
                                  }
                                  return selected;
                                }}
                                error={touched.bankAccount && !!errors.bankAccount}
                                sx={styles.select}
                              >
                                <MenuItem value="JP Morgan Chase.">
                                  JP Morgan Chase.
                                </MenuItem>
                                <MenuItem value="Bank of America">
                                  Bank of America
                                </MenuItem>
                              </Select>
                              {touched.bankAccount && errors.bankAccount && (
                                <Typography color="error" sx={styles.errorText}>
                                  {errors.bankAccount}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </Box>
                        <Typography sx={styles.payeeNameLabel}>Payee Name</Typography>

                        <Select
                          fullWidth
                          name="payeeName"
                          value={values.payeeName}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return (
                                <Typography sx={{ color: "text.secondary" }}>
                                  Select Payee Name
                                </Typography>
                              );
                            }
                            return selected;
                          }}
                          error={touched.payeeName && !!errors.payeeName}
                          sx={styles.payeeSelect}
                        >
                          <MenuItem value="Abraham Sabel">
                            Abraham Sabel
                          </MenuItem>
                          <MenuItem value="Andrew">
                            Andrew
                          </MenuItem>
                        </Select>

                        {touched.payeeName && errors.payeeName && (
                          <Typography color="error" sx={styles.errorText}>
                            {errors.payeeName}
                          </Typography>
                        )}

                      </Box>
                      <Box sx={styles.addEditHeaderRight}>
                        <Box sx={styles.itemContainer}>
                          <Typography sx={styles.itemLabel}>
                            Check <br /> Number
                          </Typography>
                          <TextField
                            fullWidth
                            name="checkNumber"
                            placeholder="Check Number"
                            value={values.checkNumber}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.checkNumber && !!errors.checkNumber}
                            helperText={touched.checkNumber && errors.checkNumber}
                            disabled={true}
                            sx={{
                              ...styles.input,
                              opacity: isblank ? 0.5 : 1,
                              "& .MuiInputBase-input": {
                                color: "#CECECE",
                              },
                              "& .MuiInputLabel-root": {
                                color: "#CECECE",
                              },
                            }}
                          />
                          <Typography sx={{ ...styles.checkNumberAutomatic, color: isblank ? "#CECECE" : "#000000DE" }} >
                            (Automatic)
                          </Typography>
                        </Box>

                        <Box sx={styles.itemContainer}>
                          <Typography sx={styles.itemLabel}>
                            Issued <br /> Date
                          </Typography>
                          <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                              value={values.issuedDate ? dayjs(values.issuedDate) : null}
                              onChange={(newValue) => {
                                const formattedDate = newValue ? newValue.format('MM/DD/YYYY') : '';
                                setFieldValue('issuedDate', formattedDate);
                              }}
                              slotProps={{
                                textField: {
                                  fullWidth: true,
                                  placeholder: "Issued Date",
                                  error: touched.issuedDate && !!errors.issuedDate,
                                  helperText: touched.issuedDate && errors.issuedDate,
                                  sx: styles.input
                                }
                              }}
                            />
                          </LocalizationProvider>
                        </Box>

                        <Box sx={{ ...styles.itemContainer, alignItems: 'center' }}>
                          <Typography sx={styles.itemLabel}>
                            Amount
                          </Typography>
                          <TextField
                            fullWidth
                            name="amount"
                            placeholder="Amount"
                            value={values.amount}
                            onChange={(e) => {
                              // Only allow numbers and one decimal point
                              const value = e.target.value;
                              if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                handleChange(e);
                                setFieldValue("dollarAmount", numberToCurrencyWords(Number(value)));
                              }
                            }}
                            onBlur={(e) => {
                              const formattedValue = formatAmountAsCurrency(e.target.value);
                              setFieldValue('amount', formattedValue);
                              setFieldValue("dollarAmount", numberToCurrencyWords(Number(formattedValue)));
                            }}
                            error={touched.amount && !!errors.amount}
                            helperText={touched.amount && errors.amount}
                            sx={styles.input}
                            InputProps={{
                              startAdornment: <InputAdornment position="start">$</InputAdornment>,
                              inputMode: 'decimal',
                              pattern: '[0-9]*\\.?[0-9]*'
                            }}
                          />
                        </Box>

                      </Box>
                    </Box>

                    <Box sx={styles.dollarsContainer}>
                      <TextField
                        fullWidth
                        name="dollarAmount"
                        placeholder=""
                        value={values.dollarAmount}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.dollarAmount && !!errors.dollarAmount}
                        helperText={touched.dollarAmount && errors.dollarAmount}
                        sx={styles.dollarInput}
                      />
                      <Typography sx={styles.dollarText}>
                        Dollars
                      </Typography>
                    </Box>

                    <Box sx={styles.memoContainer}>
                      <Box>
                        <Typography sx={styles.memoLabel}>Memo</Typography>
                        <TextField
                          fullWidth
                          name="memo"
                          placeholder=""
                          value={values.memo}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.memo && !!errors.memo}
                          helperText={touched.memo && errors.memo}
                          sx={styles.memoInput}
                        />
                      </Box>
                      <Box>
                        <Typography sx={styles.checkTitle}>
                          Your Signature
                        </Typography>
                        <Typography sx={styles.checkSubTitle}>
                          Want to sign with signature
                        </Typography>

                        <Box sx={styles.radioGroup}>
                          <Box sx={{ ...styles.radioItem, opacity: isblank ? 0.5 : 1 }}>
                            <Radio
                              name="signature"
                              value={true}
                              checked={values.signature === true}
                              onChange={(e) => {
                                setFieldValue("signature", true);
                              }}
                              disabled={isblank}
                              sx={styles.radio}
                            />
                            <Typography sx={styles.radioLabel}>Yes</Typography>
                          </Box>
                          <Box sx={{ ...styles.radioItem, opacity: isblank ? 0.5 : 1 }}>
                            <Radio
                              name="signature"
                              value={false}
                              checked={values.signature === false}
                              onChange={(e) => {
                                setFieldValue("signature", false);
                              }}
                              disabled={isblank}
                              sx={styles.radio}
                            />
                            <Typography sx={styles.radioLabel}>No</Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Box>

                    <Typography sx={styles.footerNumber}>
                      {`{“000012”} {“1234567890”} {“1234567890”}`}
                    </Typography>
                  </Box>

                  <Box sx={styles.checkBottomContainer}>
                    <Box sx={styles.checkBottomItemContainer}>
                      <Typography sx={styles.checkBottomItemlabel}>Tags</Typography>
                      <Select
                        fullWidth
                        name="tags"
                        value={values.tags}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        displayEmpty
                        multiple
                        renderValue={(selected) => {
                          if (!selected || selected.length === 0) {
                            return (
                              <Typography sx={{ color: "text.secondary" }}>
                                Select Tags
                              </Typography>
                            );
                          }
                          return <Box sx={{ display: "flex", gap: '12px', alignItems: 'center' }}>
                            {
                              selected.slice(0, 3).map((tag, index) =>
                                <Box key={"tag_input" + index} sx={styles.selectTagItem}>
                                  <Box sx={{ ...styles.tag, backgroundColor: (otherTags.find((val) => val.value === tag) || myTags.find((val) => val.value === tag))?.color }} />
                                  {tag}
                                </Box>)
                            }
                            {selected.length > 3 && <Typography sx={{ color: "#000000DE" }}>+{selected.length - 3}</Typography>}
                          </Box>;
                        }}
                        error={touched.tags && !!errors.tags}
                        sx={{ ...styles.select, width: '100% !important' }}
                        MenuProps={{
                          sx: {
                            '& .MuiMenu-paper': {
                              width: '210px',
                              minWidth: '210px !important'
                            }
                          },
                          anchorOrigin: {
                            vertical: 'bottom',
                            horizontal: 'right'
                          },
                          transformOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                          },
                        }}
                      >
                        <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                          <TagIcon />
                          <Typography sx={styles.tagTitle}>Other Tags</Typography>
                        </Box>
                        {
                          otherTags.map((tag, index) => (
                            <MenuItem key={index} value={tag.value}>
                              <Box sx={styles.tagItem}>
                                <Checkbox checked={(values.tags || []).includes(tag.value)} />
                                <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                <Typography sx={styles.tagText}>{tag.value}</Typography>
                              </Box>
                            </MenuItem>
                          ))
                        }
                        <Divider sx={{ m: 0 }} />
                        <Box sx={{ ...styles.tagItem, my: 1, ml: '30px' }}>
                          <TagIcon />
                          <Typography sx={styles.tagTitle}>My Tags</Typography>
                        </Box>
                        {
                          myTags.map((tag, index) => (
                            <MenuItem key={index} value={tag.value}>
                              <Box sx={styles.tagItem}>
                                <Checkbox checked={(values.tags || []).includes(tag.value)} />
                                <Box sx={{ ...styles.tag, backgroundColor: tag.color }} />
                                <Typography sx={styles.tagText}>{tag.value}</Typography>
                              </Box>
                            </MenuItem>
                          ))
                        }

                      </Select>
                      {touched.tags && errors.tags && (
                        <Typography color="error" sx={styles.errorText}>
                          {errors.tags}
                        </Typography>
                      )}
                    </Box>
                    <Box sx={styles.checkBottomItemContainer}>
                      <Typography sx={styles.checkBottomItemlabel}>Invoice ID</Typography>
                      <TextField
                        fullWidth
                        name="invoiceId"
                        placeholder="Invoice Id"
                        value={values.invoiceId}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.invoiceId && !!errors.invoiceId}
                        helperText={touched.invoiceId && errors.invoiceId}
                        sx={{
                          ...styles.input,
                          width: '100% !important'
                        }}
                      />
                    </Box>
                    <Box sx={{display: 'flex'}}>
                      <Box
                        sx={{
                          ...styles.addAttachmentButton,
                          borderColor: values.attachments.length > 0 ? '#204464' : '#00000099',
                          borderStyle: isDragging ? 'dashed' : 'dashed',
                          borderWidth: isDragging ? '2px' : '1px',
                          backgroundColor: isDragging ? '#F0F9FF' : 'transparent',
                        }}
                        onDragEnter={handleDragEnter}
                        onDragLeave={handleDragLeave}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                        onClick={() => setOpenAttachmentsModal(true)}
                      >
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileInput}
                          style={{ display: 'none' }}
                          multiple
                        />
                        <DocumentIcon color={values.attachments.length > 0 ? '#204464' : '#00000099'} />
                        <Typography sx={{ fontSize: '18px', color: values.attachments.length > 0 ? '#204464' : '#00000099', lineHeight: 'normal' }}>
                          {(values.attachments && values.attachments.length > 0
                            ? `${values.attachments.length} Attachments`
                            : 'Add Attachments')}
                        </Typography>
                        <AttachFileOutlinedIcon sx={{ fontSize: '20px', color: values.attachments.length > 0 ? '#204464' : '#00000099', rotate: '45deg' }} />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            )}
          </Formik>
        }
        actions={
          <Box sx={styles.actionsContainer}>
            <Button
              onClick={handleSaveAndPrint}
              variant="outlined"
              sx={styles.cancelButton}
            >
              Save & Print
            </Button>

            <CustomButton
              variant="outlined"
              color="primary"
              onClick={handleSaveCheck}
            >
              Save Check
            </CustomButton>
          </Box>
        }
      />
      <AttachmentsModal
        open={openAttachmentsModal}
        onClose={() => setOpenAttachmentsModal(false)}
        title="Add Attachments"
        handleSave={handleSaveAttachments}
        attachments={formRef.current?.values.attachments || []}
      />
    </>
  );
};
