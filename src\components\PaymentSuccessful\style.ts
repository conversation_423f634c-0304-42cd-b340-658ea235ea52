export const styles = {
  alertIconContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "120px",
    mb: "16px",
  },
  alertIconBorder: {
    width: "120px",
    height: "120px",
    backgroundColor: "#00CF721D",
    borderRadius: "50%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  alertIconMain: {
    width: "100px",
    height: "100px",
    backgroundColor: "#00CF72",
    borderRadius: "50%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    "& .MuiSvgIcon-root": {
      color: "#fff",
      width: "50px",
      height: "50px",
    },
  },
  title: {
    color: '#000000DE',
    fontSize: '40px',
    fontWeight: 600,
    my: 1,
  },
  description : {
    color: '#00000099',
    fontSize: '20px',
    fontWeight: 500,
    my: 2,
  },
  boxInfo : {
    border : '1px solid #CECECE',
    backgroundColor: '#F9FAFB',
    borderRadius: '8px',
    padding: '30px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    width: '500px',
  },
  itemContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItem: 'center'
  },
  confirmButton: {
    fontSize: '14px',
    lineHeight: '20px',
    height: '40px',
    width: '100px',
    textTransform: 'none',
    borderRadius: '6px',
    backgroundColor: '#204464',
    '&:hover': {
      backgroundColor: '#1a3850'
    }
  },
}