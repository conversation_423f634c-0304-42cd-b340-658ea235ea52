// React-specific TypeScript types and interfaces

import { ReactNode, ComponentProps } from 'react';

// Common React component props
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  'data-testid'?: string;
}

// Layout component props
export interface LayoutProps extends BaseComponentProps {
  title?: string;
  showSidebar?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
}

// Page component props
export interface PageProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  loading?: boolean;
  error?: string | null;
}

// Form component props
export interface FormProps extends BaseComponentProps {
  onSubmit: (data: any) => void;
  initialValues?: Record<string, any>;
  validationSchema?: any;
  loading?: boolean;
  disabled?: boolean;
}

// Modal component props
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  closable?: boolean;
  footer?: ReactNode;
}

// Card component props
export interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  elevation?: number;
  variant?: 'outlined' | 'elevation';
}

// List component props
export interface ListProps<T = any> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  loading?: boolean;
  empty?: ReactNode;
  keyExtractor?: (item: T, index: number) => string;
}

// Navigation props
export interface NavigationItem {
  id: string;
  label: string;
  icon?: ReactNode;
  path?: string;
  children?: NavigationItem[];
  disabled?: boolean;
  badge?: string | number;
}

export interface NavigationProps extends BaseComponentProps {
  items: NavigationItem[];
  activeItem?: string;
  onItemClick?: (item: NavigationItem) => void;
  collapsed?: boolean;
}

// Data display props
export interface DataDisplayProps<T = any> extends BaseComponentProps {
  data: T;
  loading?: boolean;
  error?: string | null;
  empty?: ReactNode;
}

// Search and filter props
export interface SearchProps extends BaseComponentProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSearch?: (value: string) => void;
  loading?: boolean;
  suggestions?: string[];
}

export interface FilterProps<T = any> extends BaseComponentProps {
  filters: T;
  onChange: (filters: T) => void;
  onReset?: () => void;
  onApply?: (filters: T) => void;
}

// Utility types for React components
export type ComponentWithChildren<P = {}> = P & { children: ReactNode };
export type ComponentWithClassName<P = {}> = P & { className?: string };
export type ComponentWithTestId<P = {}> = P & { 'data-testid'?: string };

// Higher-order component types
export type HOCProps<P = {}> = P & BaseComponentProps;
export type WithLoadingProps<P = {}> = P & { loading?: boolean };
export type WithErrorProps<P = {}> = P & { error?: string | null };

// Event handler types for React
export type ReactChangeHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void;
export type ReactClickHandler<T = HTMLButtonElement> = (event: React.MouseEvent<T>) => void;
export type ReactSubmitHandler<T = HTMLFormElement> = (event: React.FormEvent<T>) => void;
export type ReactKeyboardHandler<T = HTMLElement> = (event: React.KeyboardEvent<T>) => void;

// Ref types
export type RefType<T> = React.RefObject<T> | React.MutableRefObject<T>;
export type ForwardedRefType<T> = React.ForwardedRef<T>;

// Component type helpers
export type ComponentPropsWithRef<T extends React.ElementType> = ComponentProps<T> & {
  ref?: React.Ref<React.ComponentRef<T>>;
};

export type PolymorphicComponentProps<T extends React.ElementType, P = {}> = P & {
  as?: T;
} & Omit<ComponentProps<T>, keyof P | 'as'>;

// Theme-related types
export interface ThemeProps {
  theme?: 'light' | 'dark';
}

// Responsive props
export interface ResponsiveProps {
  xs?: boolean | number;
  sm?: boolean | number;
  md?: boolean | number;
  lg?: boolean | number;
  xl?: boolean | number;
}

// Animation props
export interface AnimationProps {
  animate?: boolean;
  duration?: number;
  delay?: number;
  easing?: string;
}

// Accessibility props
export interface A11yProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  role?: string;
  tabIndex?: number;
}
