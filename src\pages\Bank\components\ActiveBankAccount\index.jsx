import React, { useState } from "react";
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  Pagination,
  PaginationItem,
} from "@mui/material";

import { MoreVerticalIcon } from "../../../../components/Icons";
import { CustomTable } from "../../../../components/table/CustomTable";
import { DeleteModal } from "../modals";
import { AddEditModal } from "../modals";
import { styles } from "../../styles";

const mockChecks = [
  {
    id: 1,
    nickName: "<PERSON>",
    bankName: "Bank of America",
    accountNumber: "**********",
    availableBalance: "$1000.00",
    actions: "",

    type: "USA",
    routingNumber: "*********",
    confirmRoutingNumber: "*********",
    confirmAccountNumber: "**********",
    accountType: "Personal Checking",
    checkNoGeneration: "manual", // or "auto" as default
    checkNoLength: "0001",
    checkNoStart: "1000",
    signature: "data:image/png;base64,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",
    signatureEnabled: true,
  },
  {
    id: 2,
    nickName: "Abraham Sabel",
    bankName: "Bank of America",
    accountNumber: "**********",
    availableBalance: "$1000.00",
    actions: "",

    type: "CANADA",
    transitNumber: "*********",
    confirmTransitNumber: "*********",
    financialInstitutionNumber: "*********",
    confirmFinancialInstitutionNumber: "*********",
    confirmAccountNumber: "**********",
    accountType: "Personal Checking",
    checkNoGeneration: "manual", // or "auto" as default
    checkNoLength: "0001",
    checkNoStart: "1000",
    signature: "data:image/png;base64,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",
    signatureEnabled: true,
  },
  {
    id: 3,
    nickName: "Abraham Sabel",
    bankName: "Bank of America",
    accountNumber: "**********",
    availableBalance: "$1000.00",
    actions: "",

    type: "USA",
    routingNumber: "*********",
    confirmRoutingNumber: "*********",
    confirmAccountNumber: "**********",
    accountType: "Personal Checking",
    checkNoGeneration: "auto", // or "auto" as default
    checkNoLength: "0001",
    checkNoStart: "1000",
    signature: null,
    signatureEnabled: false,
  },
  {
    id: 4,
    nickName: "Abraham Sabel",
    bankName: "Bank of America",
    accountNumber: "**********",
    availableBalance: "$1000.00",
    actions: "",

    type: "CANADA",
    transitNumber: "*********",
    confirmTransitNumber: "*********",
    financialInstitutionNumber: "*********",
    confirmFinancialInstitutionNumber: "*********",
    confirmAccountNumber: "**********",
    accountType: "Personal Checking",
    checkNoGeneration: "auto", // or "auto" as default
    checkNoLength: "0001",
    checkNoStart: "1000",
    signature: null,
    signatureEnabled: false,
  },
  
];
const ActiveBankAccount = () => {
  const [checks, setChecks] = useState(mockChecks);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [page, setPage] = useState(1);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);

  const handleMenuOpen = (event, id) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditAccount = () => {
    setOpenEditModal(true);
    setAnchorEl(null);
  };

  const handleDeactivateAccount = () => {
    setAnchorEl(null);
  };

  const handleDeleteAccount = () => {
    setOpenDeleteModal(true);
    setAnchorEl(null);
  };

  const handleConfirmDelete = () => {
    console.log("delete account", selectedItem);
    setOpenDeleteModal(false);
  };

  const renderHeaderCell = (column) => column.label;

  const renderCell = (row, column) => {
    switch (column.id) {
      case "actions":
        return (
          <IconButton
            sx={styles.moreButton}
            onClick={(e) => handleMenuOpen(e, row)}
          >
            <MoreVerticalIcon />
          </IconButton>
        );
      default:
        return row[column.id];
    }
  };

  return (
    <Box sx={styles.tableWrapper}>
      <CustomTable
        columns={[
          { id: "nickName", label: "Nick Name" },
          { id: "bankName", label: "Bank Name" },
          { id: "accountNumber", label: "Account Number" },
          { id: "availableBalance", label: "Available Balance" },
          { id: "actions", label: "Actions" },
        ]}
        data={checks}
        renderCell={renderCell}
        renderHeaderCell={renderHeaderCell}
        isCenteredCells={true}
      />
      <Box sx={styles.paginationContainer}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                previous: () => "Previous",
                next: () => "Next",
              }}
              {...item}
              sx={styles.paginationItem}
            />
          )}
          sx={styles.pagination}
        />
      </Box>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: styles.menuPaper,
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleEditAccount} sx={styles.menuItem}>
          Edit Account
        </MenuItem>
        <MenuItem onClick={handleDeactivateAccount} sx={styles.menuItem}>
          Deactivate Bank Account
        </MenuItem>
        <MenuItem onClick={handleDeleteAccount} sx={styles.menuItem}>
          Delete Account
        </MenuItem>
      </Menu>

      <AddEditModal
        open={openEditModal}
        onClose={() => setOpenEditModal(false)}
        onConfirm={() => setOpenEditModal(false)}
        editMode={true}
        data={selectedItem}
      />

      <DeleteModal
        open={openDeleteModal}
        onClose={() => setOpenDeleteModal(false)}
        onConfirm={handleConfirmDelete}
      />
    </Box>
  );
};

export default ActiveBankAccount;
