import React from 'react';
import { <PERSON><PERSON>, Box, ButtonProps } from '@mui/material';
import { styles } from './styles';

interface CustomButtonProps extends Omit<ButtonProps, 'startIcon' | 'endIcon' | 'color' | 'variant' | 'size'> {
  children: React.ReactNode;
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  size?: 'small' | 'medium' | 'large';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  sx?: object;
}

export const CustomButton: React.FC<CustomButtonProps> = ({ 
  children, 
  variant = 'outlined',
  color = 'primary',
  size = 'medium',
  startIcon = null,
  endIcon = null,
  onClick,
  sx = {},
  ...props 
}) => {
  const getStyles = () => {
    // Create an array of style objects with proper typing
    const buttonStyles: Record<string, any>[] = [styles.button];
    
    // Add variant styles based on color prop
    if (color === 'primary') buttonStyles.push(styles.primaryVariant);
    if (color === 'secondary') buttonStyles.push(styles.secondaryVariant);
    if (color === 'error') buttonStyles.push(styles.errorVariant);
    // if (color === 'warning') buttonStyles.push(styles.warningVariant);
    
    // Add size styles
    if (size === 'small') buttonStyles.push(styles.smallSize);
    
    // Merge all style objects with the sx prop
    return Object.assign({}, ...buttonStyles, sx);
  };

  return (
    <Button
      variant={variant}
      onClick={onClick}
      size={size}
      color={color}
      sx={getStyles()}
      {...props}
    >
      {startIcon && (
        <Box component="span" sx={{ display: 'flex', alignItems: 'center'}}>
          {startIcon}
        </Box>
      )}
      <Box sx={startIcon || endIcon ? styles.buttonIconText : undefined}>
        {children}
      </Box>
      {endIcon && (
        <Box component="span" sx={{ display: 'flex', alignItems: 'center'}}>
          {endIcon}
        </Box>
      )}
    </Button>
  );
}; 
