import { BrowserRouter as Router, Switch, Route } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import MainLayout from './components/layout/MainLayout';
import theme from './theme';
import UserManagement from './pages/UserManagement';
import Profile from './pages/Profile';
import MyChecks from './pages/MyChecks';
import AllMails from './pages/AllMails';
import MailManagement from './pages/MailManagement';
import BankAccount from './pages/Bank';
import Dashboard from './pages/Dashboard';
import PaymentLinks from './pages/PaymentLinks';
import './styles/global.css';

const Settings = () => <div>Settings Page</div>;

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <MainLayout>
          <Switch>
            <Route exact path="/" component={Dashboard} />
            <Route path="/dashboard" component={Dashboard} />
            <Route path="/profile" component={Profile} />
            <Route path="/settings" component={Settings} />
            <Route path="/user-management" component={UserManagement} />
            <Route path="/my-checks" component={MyChecks} />
            <Route path="/all-mails" component={AllMails} />
            <Route path="/mail-management" component={MailManagement} />
            <Route path="/bank-account" component={BankAccount} />
            <Route path="/payment-link" component={PaymentLinks} />
          </Switch>
        </MainLayout>
      </Router>
    </ThemeProvider>
  );
}

export default App; 
