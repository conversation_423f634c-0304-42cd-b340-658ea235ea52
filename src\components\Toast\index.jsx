import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { hideToast } from '../../redux/slices/toastSlice';
import Snackbar from '@mui/material/Snackbar';
import { styles } from './styles';
import { Alert } from '@mui/material';

const Toast = () => {

  const dispatch = useDispatch();
  const { showToast, message, type } = useSelector((state) => state.toast);

  const getStyles = () => {
    const toastStyles = [styles.toast];
    if (type === 'success') toastStyles.push(styles.successToast);
    if (type === 'error') toastStyles.push(styles.errorToast);
    return toastStyles;
  };

  return (
    <Snackbar
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      open={showToast}
      autoHideDuration={3000}
      onClose={() => dispatch(hideToast())}
      // message={message}
      sx={styles.snackbar}
    >
      <Alert
        onClose={() => dispatch(hideToast())}
        severity={type}
        sx={getStyles()}
      >
        {message}
      </Alert>
    </Snackbar>
  );
};

export default Toast;
