import {
  Box,
  Typography,
} from "@mui/material";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { styles } from "../../../styles";

import { CustomTable } from "../../../../../components/table/CustomTable";
import { CustomButton } from "../../../../../components/buttons/CustomButton";

export const BatchErrors = ({ open, onClose, failedChecks = [
  {no: '11',payeeName: '<PERSON>', account: '<PERSON>Doe123', issuedDate: '2/12/2025' },
  {no: '12',payeeName: '<PERSON>', account: 'JohnDoe123', issuedDate: '2/12/2025' },
  {no: '13',payeeName: '<PERSON>', account: '<PERSON>Doe123', issuedDate: '2/12/2025' },
  {no: '14',payeeName: '<PERSON>', account: '<PERSON><PERSON>oe123', issuedDate: '2/12/2025' },
  {no: '15',payeeName: '<PERSON>', account: '<PERSON><PERSON>oe123', issuedDate: '2/12/2025' },
  {no: '16',payeeName: '<PERSON>', account: '<PERSON><PERSON>oe123', issuedDate: '2/12/2025' },
  {no: '29',payeeName: 'Abraham Sabel', account: 'JohnDoe123', issuedDate: '2/12/2025' },

] }) => {

  const renderCell = (row, column) => {
    switch (column.id) {
      case "status":
        return (
          <Typography
            size="small"
            sx={{
              color: "#F03D3E",
            }}
          >
            Failed
          </Typography>
        );
      default:
        return row[column.id];
    }
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      width={765}
      title="Batch Creation Failed"
      content={
        <Box sx={styles.content}>
          <Box sx={styles.section}>
            <Typography sx={styles.dialogDescription}>
              All checks in this batch have failed. No batch has been created. Please review the details and try again.
            </Typography>
            <CustomTable
              columns={[
                { id: "no", label: "Check No." },
                { id: "payeeName", label: "Payee Name" },
                {
                  id: "account",
                  label: "User Account",
                },
                { id: "issuedDate", label: "Issued Date" },
                { id: "status", label: "Status" },
              ]}
              data={failedChecks}
              isCenteredCells={true}
              renderCell={renderCell}
            />
          </Box>
        </Box>
      }
      actions={
        <>
          <CustomButton
            variant="outlined"
            color="primary"
            onClick={onClose}
          >
            Cancel
          </CustomButton>
        </>
      }
    />
  );
};
