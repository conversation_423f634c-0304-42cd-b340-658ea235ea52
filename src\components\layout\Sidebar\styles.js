export const styles = {
  drawer: {
    width: '300px',
    flexShrink: 0,
    '& .MuiDrawer-paper': {
      width: '300px',
      boxSizing: 'border-box',
      borderRight: '1px solid #E5E7EB',
      position: 'relative',
    }
  },
  sidebar: {
    height: '100vh',
    backgroundColor: '#fff',
    padding: '20px 16px',
    display: 'flex',
    flexDirection: 'column'
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    marginBottom: '32px',
    paddingLeft: '12px'
  },
  logo: {
    width: '32px',
    height: '32px'
  },
  companyName: {
    fontSize: '20px',
    lineHeight: '24px',
    fontWeight: 600,
    color: '#111827'
  },
  menuList: {
    padding: 0,
  },
  menuItem: {
    height: '50px',
    borderRadius: '6px',
    padding: '8px 20px',
    cursor: 'pointer',
    display: 'flex',
    gap: '24px',
    '&:hover': {
      backgroundColor: '#F3F4F6'
    }

  },
  menuIcon: {
    minWidth: '24px',
    height: '24px',
    color: '#6B7280',
  },
  menuText: {
    '& .MuiTypography-root': {
      fontSize: '14px',
      lineHeight: '20px',
      color: '#111827',
      textAlign: 'left'  // Text alignment for menu items
    }
  },
  divider: {
    margin: '16px 0',
    borderColor: '#E5E7EB'
  },
  sectionTitle: {
    fontSize: '12px',
    lineHeight: '16px',
    color: '#6B7280',
    textAlign: 'left',
    paddingLeft: '12px',
    fontWeight: 500
  }
}; 