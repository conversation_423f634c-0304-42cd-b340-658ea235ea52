import { Box, Typography, Tabs, Tab, Pagination, PaginationItem } from '@mui/material';
import { useState } from 'react';
import { styles } from './styles';
import { AllCheckMailList } from './components/AllCheckMailList';
import { ProcessingBatchList } from './components/ProcessingBatchList';
import { MailedCheckList } from './components/MailedCheckList';
import { CustomButton } from '../../components/buttons/CustomButton';
import { FilterIcon, PrintIcon } from '../../components/Icons';
import { BatchPrintModal } from './components/modals/BatchPrintModal';
import { BatchCreationModal } from './components/modals/BatchCreationModal';
import { BatchCreateSuccessfullyModal } from './components/modals/BatchCreateSuccessfullyModal';
import { BatchFailedModal } from './components/modals/BatchFailedModal';
import { BatchSuccesswithErrorsModal } from './components/modals/BatchSuccesswithErrorsModal';
import { BatchErrors } from './components/modals/BatchErrors';
import { sleep } from '../../common/utils'

export const getStatusColor = (status) => {
  switch (status) {
    case "Processing":
      return "#EF6C00";
    case "Submitted":
      return "#058205";
    case "Mailed":
      return "#D2BA00";
    case "Cancelled":
      return "#F03D3E";
    case "Error":
      return "#FF1014";
    default:
      return "#000000DE";
  }
};
const MailManagement = () => {
  const [page, setPage] = useState(1);
  const [currentTab, setCurrentTab] = useState("all");

  const [selectedChecks, setSelectedChecks] = useState([]);
  const [openBatchPrintDialog, setOpenBatchPrintDialog] = useState(false);
  const [openBatchCreationModal, setOpenBatchCreationModal] = useState(false);
  const [openBatchCreateSuccessfullyModal, setOpenBatchCreateSuccessfullyModal] = useState(false);
  const [openBatchFailedModal, setOpenBatchFailedModal] = useState(false);
  const [openBatchSuccesswithErrorsModal, setOpenBatchSuccesswithErrorsModal] = useState(false);
  const [openBatchErrorsModal, setOpenBatchErrorsModal] = useState(false);

  const handleTabChange = (event, newTab) => {
    setCurrentTab(newTab);
  };

  const handleConfirmPrint = async () => {
    setOpenBatchPrintDialog(false);
    setOpenBatchCreationModal(true);
    await sleep(2000)
    setOpenBatchCreationModal(false);
    setOpenBatchCreateSuccessfullyModal(true);
    setOpenBatchFailedModal(true);
    setOpenBatchSuccesswithErrorsModal(true);
  }

  const handleBatchPrint = () => {
    setOpenBatchPrintDialog(true);
  }

  const handleBatchPrintAll = () => {
    setOpenBatchPrintDialog(true);
  }


  return (
    <Box sx={styles.wrapper}>
      <Typography sx={styles.title}>Admin Mailing Management</Typography>

      <Typography
        variant="body1"
        color="text.secondary"
        sx={styles.description}
      >
        Manage and process check mailing requests efficiently. View pending,
        processing, and mailed checks, batch print requests, and track mailing
        status in one place.
      </Typography>

      <Box sx={styles.tabContainer}>
        <Tabs value={currentTab} onChange={handleTabChange} sx={styles.tabs}>
          <Tab value="all" label="All (43)" className="tab-all" />
          <Tab value="submitted" label="Submitted (04)" className="tab-submitted" />
          <Tab
            value="processing"
            label="Processing (06)"
            className="tab-processing"
          />
          <Tab value="error" label="Error (09)" className="tab-error" />
          <Tab value="mailed" label="Mailed (05)" className="tab-mailed" />
          <Tab value="cancelled" label="Cancelled (09)" className="tab-cancelled" />
        </Tabs>
        <CustomButton
          variant="outlined"
          color="primary"
          onClick={() => {
          }}
        >
          Batches (10)
        </CustomButton>
      </Box>

      <Box sx={styles.buttonContainer}>
        {(currentTab === "all" || currentTab === "submitted") && <CustomButton
          startIcon={<PrintIcon color="#000000DE" />}
          variant="outlined"
          sx={styles.actionButton}
          onClick={handleBatchPrintAll}
        >
          Batch & Print All
        </CustomButton>}
        {(currentTab === "all" || currentTab === "submitted") &&
          <CustomButton
            startIcon={<PrintIcon color={selectedChecks.length === 0 ? "#0000001A" : "#000000DE"} />}
            variant="outlined"
            sx={styles.actionButton}
            disabled={selectedChecks.length === 0}
            onClick={handleBatchPrint}
          >
            Batch Print
          </CustomButton>
        }

        <CustomButton
          variant="outlined"
          endIcon={<FilterIcon color="#000000DE" />}
          // onClick={handleFilterClick}
          color="primary"
          sx={styles.filterButton}
        >
          Filters
        </CustomButton>
      </Box>

      {currentTab === "all" && <AllCheckMailList search={"all"} setData={setSelectedChecks} />}
      {currentTab === "submitted" && <AllCheckMailList search={"submitted"} setData={setSelectedChecks} />}
      {currentTab === "processing" && <ProcessingBatchList />}
      {currentTab === "error" && <AllCheckMailList search={"error"} setData={setSelectedChecks} />}
      {currentTab === "mailed" && <MailedCheckList />}
      {currentTab === "cancelled" && <AllCheckMailList search={"cancelled"} setData={setSelectedChecks} />}
      <Box sx={styles.paginationContainer}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                previous: () => "Previous",
                next: () => "Next",
              }}
              {...item}
              sx={styles.paginationItem}
            />
          )}
          sx={styles.pagination}
        />
      </Box>

      <BatchPrintModal
        open={openBatchPrintDialog}
        onClose={() => setOpenBatchPrintDialog(false)}
        checks={selectedChecks}
        onConfirm={handleConfirmPrint}
      />

      <BatchCreationModal open={openBatchCreationModal} />

      <BatchCreateSuccessfullyModal open={openBatchCreateSuccessfullyModal} onClose={() => setOpenBatchCreateSuccessfullyModal(false)} onConfirm={() => setOpenBatchCreateSuccessfullyModal(false)} />

      <BatchFailedModal open={openBatchFailedModal} onClose={() => setOpenBatchFailedModal(false)} onConfirm={() => setOpenBatchFailedModal(false)} setOpenBatchErrorsModal={setOpenBatchErrorsModal} />

      <BatchSuccesswithErrorsModal open={openBatchSuccesswithErrorsModal} onClose={() => setOpenBatchSuccesswithErrorsModal(false)} onConfirm={() => setOpenBatchSuccesswithErrorsModal(false)} setOpenBatchErrorsModal={setOpenBatchErrorsModal} />

      <BatchErrors open={openBatchErrorsModal} onClose={() => setOpenBatchErrorsModal(false)} />
    </Box>
  );
};

export default MailManagement;
