import { Button, Box } from '@mui/material';
import { styles } from './styles';

export const CustomButton = ({ 
  children, 
  variant = 'outlined',
  color = 'primary',
  size = 'medium',
  startIcon,
  endIcon,
  onClick,
  sx = {},
  ...props 
}) => {
  const getStyles = () => {
    const buttonStyles = [styles.button];
    if (color === 'primary') buttonStyles.push(styles.primaryVariant);
    if (color === 'secondary') buttonStyles.push(styles.secondaryVariant);
    if (color === 'error') buttonStyles.push(styles.errorVariant);
    // if (color === 'warning') buttonStyles.push(styles.warningVariant);
    if (size === 'small') buttonStyles.push(styles.smallSize);
    return Object.assign({}, ...buttonStyles, sx);
  };

  return (
    <Button
      variant={variant}
      // startIcon={startIcon}
      // endIcon={endIcon}
      onClick={onClick}
      size={size}
      color={color}
      sx={getStyles()}
      {...props}
    >
      {startIcon}
      <Box sx={startIcon || endIcon ? styles.buttonIconText : null}>
        {children}
      </Box>
      {endIcon}
    </Button>
  );
}; 