import React, { useRef } from "react";
import {
  <PERSON>,
  <PERSON>pography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { AddNewModalUSA } from "./USA";
import { AddNewModalCanada } from "./Canada";
import { styles } from "../../../styles";
import CustomTabs from "../../../../../components/tabs";

export const AddEditModal = ({
  open,
  onClose,
  onConfirm,
  editMode = false,
  data = null,
}) => {
  const [activeTab, setActiveTab] = React.useState(0);
  const formRef = useRef(null);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.handleSubmit();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: styles.dialog,
      }}
    >
      <Box sx={styles.dialogHeader}>
        <DialogTitle sx={styles.dialogTitle}>
          {!editMode
            ? "Add New Bank Account"
            : `Edit ${data && data.type} Bank Account Details`}
        </DialogTitle>
        <IconButton onClick={onClose} sx={styles.dialogCloseButton}>
          <CloseIcon />
        </IconButton>
      </Box>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={styles.dialogContent}>
          <Typography color="#********" sx={{ mb: "24px" }}>
            {!editMode
              ? "Select your bank account type to proceed. Ensure the information matches your bank records for successful verification."
              : "Update your bank information for accurate check printing and transactions."}
          </Typography>

          {!editMode && (
            <CustomTabs
              activeTab={activeTab}
              handleTabChange={handleTabChange}
              styles={styles.dialogTabs}
              tabs={[{ label: "USA" }, { label: "CANADA" }]}
            />
          )}

          {!editMode ? (
            activeTab === 0 ? (
              <AddNewModalUSA formRef={formRef} onSuccess={onConfirm} />
            ) : (
              <AddNewModalCanada formRef={formRef} onSuccess={onConfirm} />
            )
          ) : data && data.type === "USA" ? (
            <AddNewModalUSA
              formRef={formRef}
              onSuccess={onConfirm}
              data={data}
              editMode
            />
          ) : (
            <AddNewModalCanada
              formRef={formRef}
              onSuccess={onConfirm}
              data={data}
              editMode
            />
          )}
        </Box>
      </DialogContent>
      <DialogActions sx={styles.dialogActions}>
        <Button onClick={onClose} variant="outlined" sx={styles.cancelButton}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={styles.confirmButton}
        >
          {!editMode ? "Add Account" : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
