import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import DashboardOutlinedIcon from '@mui/icons-material/DashboardOutlined';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import WysiwygIcon from '@mui/icons-material/Wysiwyg';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import AccountBalanceOutlinedIcon from '@mui/icons-material/AccountBalanceOutlined';
import LocalOfferOutlinedIcon from '@mui/icons-material/LocalOfferOutlined';
import LibraryBooksOutlinedIcon from '@mui/icons-material/LibraryBooksOutlined';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import WebAssetOutlinedIcon from '@mui/icons-material/WebAssetOutlined';
import MonetizationOnOutlinedIcon from '@mui/icons-material/MonetizationOnOutlined';
import PeopleOutlineOutlinedIcon from '@mui/icons-material/PeopleOutlineOutlined';
import { LogoutModal } from '../../LogoutModal';
import { LogoutIcon } from '../../Icons';
import { useHistory } from 'react-router-dom';
import { styles } from './styles';
import { useState } from 'react';

const menuItems = [
  { title: 'Dashboard', icon: <DashboardOutlinedIcon />, url: '/dashboard' },
  { title: 'Create a Check', icon: <ControlPointIcon /> },
  { title: 'My Checks', icon: <WysiwygIcon />, url: '/my-checks' },
  { title: 'Payee List', icon: <FormatListBulletedIcon /> },
  { title: 'Bank Accounts', icon: <AccountBalanceOutlinedIcon />, url: '/bank-account' },
  { title: 'My Tags', icon: <LocalOfferOutlinedIcon /> },
  { title: 'Check Register', icon: <LibraryBooksOutlinedIcon /> },
  { title: 'All Mails', icon: <LocalShippingOutlinedIcon />, url: '/all-mails' },
];

const receivablesItems = [
  { title: 'Payment Links', icon: <WebAssetOutlinedIcon />, url: '/payment-link' },
];

const integrationItems = [
  { title: 'Payments', icon: <MonetizationOnOutlinedIcon /> },
];

const adminItems = [
  { title: 'User Management', icon: <PeopleOutlineOutlinedIcon />, url: '/user-management' },
  { title: 'Mail Management', icon: <PeopleOutlineOutlinedIcon />, url: '/mail-management' },
];

const Sidebar = ({ open, onClose, variant }) => {
  const history = useHistory();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isAdmin = true;
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);

  const handleItemClick = (path) => {
    history.push(path);
    if (isMobile) {
      onClose();
    }
  };

  const handleLogout = () => {
    console.log('logout');
    // Handle logout logic here
  };

  const handleSignInAgain = () => {
    console.log('sign in again');
    // Handle sign in again logic here
  };

  const drawerContent = (
    <Box sx={styles.sidebar}>
      <Box sx={styles.logoContainer}>
        <Typography sx={styles.companyName}>
          SUNCCOS
        </Typography>
      </Box>

      <List sx={styles.menuList}>
        {menuItems.map((item) => (
          <ListItem key={item.title} sx={styles.menuItem} onClick={() => handleItemClick(item.url)}>
            <ListItemIcon sx={styles.menuIcon}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              sx={styles.menuText}
            />
          </ListItem>
        ))}
      </List>

      <Divider sx={styles.divider} />

      <Typography sx={styles.sectionTitle}>
        Receivables
      </Typography>
      <List sx={styles.menuList}>
        {receivablesItems.map((item) => (
          <ListItem key={item.title} sx={styles.menuItem}>
            <ListItemIcon sx={styles.menuIcon}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              sx={styles.menuText}
            />
          </ListItem>
        ))}
      </List>

      <Divider sx={styles.divider} />

      <Typography sx={styles.sectionTitle}>
        Integrations
      </Typography>
      <List sx={styles.menuList}>
        {integrationItems.map((item) => (
          <ListItem key={item.title} sx={styles.menuItem}>
            <ListItemIcon sx={styles.menuIcon}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              sx={styles.menuText}
            />
          </ListItem>
        ))}
      </List>

      {isAdmin && <>
        <Divider sx={styles.divider} />
        <Typography sx={styles.sectionTitle}>
          Admin
        </Typography>
        <List sx={styles.menuList}>
          {adminItems.map((item) => (
            <ListItem key={item.title} sx={styles.menuItem} onClick={() => handleItemClick(item.url)}>
              <ListItemIcon sx={styles.menuIcon}>
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.title}
                sx={styles.menuText}
              />
            </ListItem>
          ))}
        </List>
      </>}

      <Divider sx={styles.divider} />

      <List sx={styles.menuList}>
        <ListItem sx={styles.menuItem} onClick={() => setIsLogoutModalOpen(true)}>
          <ListItemIcon sx={styles.menuIcon}>
            <LogoutIcon width='100%' height='100%' color="#6B7280" />
          </ListItemIcon>
          <ListItemText
            primary="Logout"
            sx={styles.menuText}
          />
        </ListItem>
      </List>

    </Box>
  );


  return (
    <Drawer
      variant={variant}
      open={open}
      onClose={onClose}
      sx={styles.drawer}
    >
      {drawerContent}
      <LogoutModal open={isLogoutModalOpen} onClose={() => setIsLogoutModalOpen(false)} handleLogout={handleLogout} handleSignInAgain={handleSignInAgain} />
    </Drawer>
  );
};

export default Sidebar; 
