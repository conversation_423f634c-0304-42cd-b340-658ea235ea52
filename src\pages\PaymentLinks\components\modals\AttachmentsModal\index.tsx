import React, { useState, useRef, useEffect } from "react";
import { Box, Typography, IconButton, Button } from "@mui/material";
import { CustomDialog } from "../../../../../components/dialog/CustomDialog";
import { CustomButton } from "../../../../../components/buttons/CustomButton";
import { CheckDeleteIcon, EditIcon } from "../../../../../components/Icons";
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import { styles } from "../../../styles";
import { CustomTable } from "../../../../../components/table/CustomTable";
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import { DeleteAttachmentAlert } from "../DeleteAttachmentAlert";
import { EditAttachmentDescriptionModal } from "../EditAttachmentDescriptionModal";
import { getFileIcon } from "../../../../../common/fileUtils";

interface FileData {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  file?: File;
  description?: string;
}

interface AttachmentsModalProps {
  open: boolean;
  onClose: () => void;
  handleSave: (files: FileData[]) => void;
  attachments?: FileData[];
  title?: string;
}

export const AttachmentsModal: React.FC<AttachmentsModalProps> = ({
  open,
  onClose,
  handleSave,
  attachments = [],
  title = "Add Attachments"
}) => {
  const [files, setFiles] = useState<FileData[]>(attachments || []);

  // Reset files state when attachments prop changes or modal opens
  useEffect(() => {
    if (open) {
      setFiles(attachments || []);
    }
  }, [attachments, open]);

  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [openAlertModal, setOpenAlertModal] = useState<boolean>(false);
  const [openEditDescriptionModal, setOpenEditDescriptionModal] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [desId, setDesId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      processFiles(selectedFiles);
      // Reset the file input value so the same file can be selected again
      e.target.value = '';
    }
  };

  const processFiles = (newFiles: File[]): void => {
    const processedFiles = newFiles.map(file => {
      // Create URL for preview
      const url = URL.createObjectURL(file);
      return {
        name: file.name,
        size: file.size,
        type: file.type,
        url,
        file,
        id: Date.now() + Math.random().toString(36).substr(2, 9) // Add unique ID
      };
    });

    setFiles(prevFiles => [...prevFiles, ...processedFiles]);
  };

  const removeFile = (fileId: string): void => {
    setFiles(prevFiles => {
      // Find the file with the matching ID
      const fileIndex = prevFiles.findIndex(file => file.id === fileId);

      if (fileIndex === -1) return prevFiles; // File not found

      const updatedFiles = [...prevFiles];

      // Release the object URL to avoid memory leaks
      if (updatedFiles[fileIndex]?.url) {
        URL.revokeObjectURL(updatedFiles[fileIndex].url!);
      }

      // Remove the file
      updatedFiles.splice(fileIndex, 1);
      handleSave(updatedFiles);
      return updatedFiles;
    });
  };

  const handleSaveAttachments = (): void => {
    handleSave(files);
    onClose();
  };

  const handleDescriptionSave = (des: string): void => {
    setFiles(prevFiles => {
      return prevFiles.map(file => {
        if (file.id === desId) {
          return { ...file, description: des };
        }
        return file;
      });
    });
    setOpenEditDescriptionModal(false);
  };

  const handleDownloadFile = (file: FileData): void => {
    // Create a download link
    const link = document.createElement('a');

    // If it's a file object with a URL (from newly uploaded files)
    if (file.url) {
      // Set the download attributes
      link.href = file.url;
      link.download = file.name;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    // If it's a file from attachments that might have a different structure
    else if (file.file) {
      // Create a blob URL from the file object
      const blobUrl = URL.createObjectURL(file.file);
      link.href = blobUrl;
      link.download = file.name;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(blobUrl);
    }
  };

  interface Column {
    id: string;
    label: string;
  }

  const renderCell = (row: FileData, column: Column): any => {
    switch (column.id) {
      case 'name':
        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <Box sx={{
              width: '16px',
              height: '16px',
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {getFileIcon(row)}
            </Box>
            <Typography sx={styles.fileNameTable}>
              {row.name}
            </Typography>
          </Box>
        );
      case 'description':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton onClick={() => {
              setDesId(row.id);
              setOpenEditDescriptionModal(true);
            }}>
              <EditIcon color="#00000099" />
            </IconButton>
            <Typography sx={{ fontSize: '16px', color: '#000000DE' }}>{row.description ? row.description : '-'}</Typography>
          </Box>
        );
      case "action":
        return (
          <Box>
            <IconButton onClick={() => handleDownloadFile(row)}>
              <FileDownloadOutlinedIcon sx={{ fontSize: '16px' }} />
            </IconButton>
            <IconButton onClick={() => {
              setDeleteId(row.id);
              setOpenAlertModal(true);
            }}>
              <CheckDeleteIcon color="#FF4D4F" />
            </IconButton>
          </Box>
        );
      default:
        return row[column.id as keyof FileData];
    }
  };

  const renderHeaderCell = (column: Column): React.ReactNode => {
    switch (column.label) {
      case "File Name":
        return <Typography sx={{ textAlign: 'left', pl: 2, fontWeight: '600' }}>{column.label}</Typography>;
      case "Description":
        return <Typography sx={{ textAlign: 'left', pl: 2, fontWeight: '600' }}>{column.label}</Typography>;
      default:
        return column.label;
    }
  };

  return (
    <>
      <CustomDialog
        open={open}
        onClose={onClose}
        title={title}
        width='765px'
        content={
          <Box>
            <Box
              sx={{
                border: isDragging ? '2px dashed #3EA5F9' : '2px dashed #204464',
                backgroundColor: isDragging ? '#F0F9FF' : '#F9FAFB',
                ...styles.attachmentDragDrop
              }}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                <CloudUploadOutlinedIcon sx={{ fontSize: '32px', color: '#204464', mb: 1 }} />
                <Typography sx={{ fontSize: '16px', fontWeight: '600', color: '#204464', mb: 1 }}>
                  Drag and drop files here
                </Typography>
                <Typography sx={{ fontSize: '14px', color: '#00000099', mb: 2 }}>
                  or
                </Typography>
                <input
                  type="file"
                  multiple
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  onChange={handleFileInput}
                />
                <CustomButton
                  variant="outlined"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Browse Files
                </CustomButton>
              </Box>
            </Box>

            {files.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <CustomTable
                  columns={[
                    { id: 'name', label: 'File Name', width: '30%' },
                    { id: 'description', label: 'Description', width: '50%' },
                    { id: 'action', label: 'Action', width: '20%' },
                  ]}
                  data={files}
                  renderCell={renderCell}
                  renderHeaderCell={renderHeaderCell}
                  isCenteredCells={true}
                />
              </Box>
            )}
          </Box>
        }
        actions={
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={onClose}
              sx={styles.cancelButton}

            >
              Cancel
            </Button>
            <CustomButton
              variant="contained"
              onClick={handleSaveAttachments}
            >
              Save
            </CustomButton>
          </Box>
        }
      />

      <DeleteAttachmentAlert
        open={openAlertModal}
        onClose={() => setOpenAlertModal(false)}
        onConfirm={() => {
          if (deleteId) {
            removeFile(deleteId);
            setOpenAlertModal(false);
          }
        }}
      />

      <EditAttachmentDescriptionModal
        open={openEditDescriptionModal}
        onClose={() => setOpenEditDescriptionModal(false)}
        onSave={handleDescriptionSave}
        description={files.find(file => file.id === desId)?.description || ''}
      />
    </>
  );
};

export default AttachmentsModal;