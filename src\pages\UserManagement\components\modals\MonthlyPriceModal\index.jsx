import React, {useEffect, useState} from 'react'
import { CustomDialog } from '../../../../../components/dialog/CustomDialog'
import { CustomButton } from '../../../../../components/buttons/CustomButton'
import { Box, Button, InputAdornment, TextField, Typography } from '@mui/material'
import { styles } from '../../../styles'
import { formatAmountAsCurrency } from '../../../../../common/utils'

export const MonthlyPriceModal = ({ open, onClose, user }) => {

  const [price, setPrice] = useState(Number(user?.monthlyPrice).toFixed(2));

  useEffect(() => {
    setPrice(Number(user?.monthlyPrice).toFixed(2));
  },[user])

  const handleSave = () => {
    onClose()
  }

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      width={685}
      title={user?.monthlyPrice ? "Update Monthly price" : "Add Monthly price"}
      content={<Box>
        <Typography sx={styles.lable}>Subscription Price</Typography>
        <TextField
          fullWidth
          name="amount"
          placeholder="Amount"
          value={price}
          onChange={(e) => {
            const value = e.target.value.replace(/[^0-9.]/g, '');
            if (/^\d*\.?\d*$/.test(value)) {
              setPrice(value);
            }
          }}
          onBlur={(e) => {
            const formattedValue = formatAmountAsCurrency(e.target.value);
            setPrice(formattedValue);
          }}
          sx={styles.input}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>,
            inputMode: 'decimal',
            pattern: '[0-9]*\\.?[0-9]*'
          }}
        />
      </Box>}
      actions={
        <>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={styles.cancelButton}
          >
            Cancel
          </Button>
          <CustomButton
            variant="outlined"
            color="primary"
            onClick={handleSave}
          >
            Save Edit
          </CustomButton>
        </>
      }
    />
  )

}